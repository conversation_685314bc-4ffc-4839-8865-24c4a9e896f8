import { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { cn } from '../../lib/utils'
import {
  School,
  GraduationCap,
  Search,
  Sparkles,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Target,
  Brain,
  Loader2,
  Star,
  Trophy,
  Users,
  BarChart3,
  X,
  Plus,
  Filter,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

// 导入API服务
import { searchUniversitiesByKeyword } from '../../services/universityApi'
import { getMajorTree } from '../../services/majorTreeApi'
import type { University } from '../../types/university'
import type { MajorTreeNode } from '../../services/majorTreeApi'

// 智能推荐的输入类型
type RecommendationType = 'university' | 'major'

// 智能推荐状态
interface IntelligentRecommendation {
  type: RecommendationType
  selectedUniversities: University[] // 选中的大学列表
  selectedMajors: MajorTreeNode[] // 选中的专业列表
  isAnalyzing: boolean
  recommendations: SubjectCombinationRecommendation[]
}

// 推荐的选科组合
interface SubjectCombinationRecommendation {
  combination: {
    firstChoice: '物理' | '历史'
    secondChoices: string[]
    name: string
  }
  matchScore: number // 匹配度分数 0-100
  reasons: string[] // 推荐理由
  advantages: string[] // 优势
  risks: string[] // 风险提示
  majorCoverage: number // 专业覆盖率
  competitiveness: 'low' | 'medium' | 'high' // 竞争激烈程度
}

interface IntelligentRecommendationModeProps {
  recommendation: IntelligentRecommendation
  onRecommendationChange: (rec: IntelligentRecommendation) => void
  onAnalyze: () => void
}

export function IntelligentRecommendationMode({
  recommendation,
  onRecommendationChange,
  onAnalyze
}: IntelligentRecommendationModeProps) {

  // 搜索状态
  const [universitySearchKeyword, setUniversitySearchKeyword] = useState('')
  const [majorSearchKeyword, setMajorSearchKeyword] = useState('')
  const [universitySearchResults, setUniversitySearchResults] = useState<University[]>([])
  const [majorSearchResults, setMajorSearchResults] = useState<MajorTreeNode[]>([])
  const [isSearchingUniversities, setIsSearchingUniversities] = useState(false)
  const [isSearchingMajors, setIsSearchingMajors] = useState(false)
  const [showUniversityResults, setShowUniversityResults] = useState(false)
  const [showMajorResults, setShowMajorResults] = useState(false)

  // 热门学校和专业建议
  const popularUniversities = [
    '清华大学', '北京大学', '复旦大学', '上海交通大学', '浙江大学',
    '中国科学技术大学', '华中科技大学', '西安交通大学', '哈尔滨工业大学'
  ]

  const popularMajors = [
    '计算机科学与技术', '临床医学', '电子信息工程', '机械工程', '金融学',
    '法学', '会计学', '软件工程', '人工智能', '数据科学与大数据技术'
  ]

  // 搜索大学
  const searchUniversities = async (keyword: string) => {
    if (!keyword.trim()) {
      setUniversitySearchResults([])
      setShowUniversityResults(false)
      return
    }

    setIsSearchingUniversities(true)
    try {
      const response = await searchUniversitiesByKeyword(keyword, 1, 10)
      setUniversitySearchResults(response.Data || [])
      setShowUniversityResults(true)
    } catch (error) {
      console.error('搜索大学失败:', error)
      setUniversitySearchResults([])
    } finally {
      setIsSearchingUniversities(false)
    }
  }

  // 搜索专业
  const searchMajors = async (keyword: string) => {
    if (!keyword.trim()) {
      setMajorSearchResults([])
      setShowMajorResults(false)
      return
    }

    setIsSearchingMajors(true)
    try {
      const response = await getMajorTree(keyword, '', '本科(普通)')
      // 扁平化专业树结构
      const flatMajors: MajorTreeNode[] = []
      const flattenMajors = (nodes: MajorTreeNode[]) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            flattenMajors(node.children)
          } else {
            flatMajors.push(node)
          }
        })
      }

      if (Array.isArray(response.data)) {
        flattenMajors(response.data)
      }

      setMajorSearchResults(flatMajors)
      setShowMajorResults(true)
    } catch (error) {
      console.error('搜索专业失败:', error)
      setMajorSearchResults([])
    } finally {
      setIsSearchingMajors(false)
    }
  }

  // 处理大学搜索输入
  const handleUniversitySearch = (value: string) => {
    setUniversitySearchKeyword(value)
    if (value.length >= 2) {
      searchUniversities(value)
    } else {
      setUniversitySearchResults([])
      setShowUniversityResults(false)
    }
  }

  // 处理专业搜索输入
  const handleMajorSearch = (value: string) => {
    setMajorSearchKeyword(value)
    if (value.length >= 2) {
      searchMajors(value)
    } else {
      setMajorSearchResults([])
      setShowMajorResults(false)
    }
  }

  // 选择大学
  const handleUniversitySelect = (university: University) => {
    const isAlreadySelected = recommendation.selectedUniversities.some(u => u.DataId === university.DataId)
    if (isAlreadySelected) return

    onRecommendationChange({
      ...recommendation,
      selectedUniversities: [...recommendation.selectedUniversities, university],
      recommendations: [] // 清空之前的推荐
    })
    setUniversitySearchKeyword('')
    setShowUniversityResults(false)
  }

  // 选择专业
  const handleMajorSelect = (major: MajorTreeNode) => {
    const isAlreadySelected = recommendation.selectedMajors.some(m => m.id === major.id)
    if (isAlreadySelected) return

    onRecommendationChange({
      ...recommendation,
      selectedMajors: [...recommendation.selectedMajors, major],
      recommendations: [] // 清空之前的推荐
    })
    setMajorSearchKeyword('')
    setShowMajorResults(false)
  }

  // 移除选中的大学
  const handleRemoveUniversity = (universityId: string) => {
    onRecommendationChange({
      ...recommendation,
      selectedUniversities: recommendation.selectedUniversities.filter(u => u.DataId !== universityId),
      recommendations: []
    })
  }

  // 移除选中的专业
  const handleRemoveMajor = (majorId: string) => {
    onRecommendationChange({
      ...recommendation,
      selectedMajors: recommendation.selectedMajors.filter(m => m.id !== majorId),
      recommendations: []
    })
  }

  // 处理类型切换
  const handleTypeChange = (type: RecommendationType) => {
    onRecommendationChange({
      ...recommendation,
      type,
      selectedUniversities: [],
      selectedMajors: [],
      recommendations: []
    })
    // 清空搜索状态
    setUniversitySearchKeyword('')
    setMajorSearchKeyword('')
    setUniversitySearchResults([])
    setMajorSearchResults([])
    setShowUniversityResults(false)
    setShowMajorResults(false)
  }

  // 生成模拟推荐数据
  const generateMockRecommendations = (type: RecommendationType): SubjectCombinationRecommendation[] => {
    // 这里应该是真实的智能分析算法
    // 现在使用模拟数据
    if (type === 'university') {
      return generateUniversityBasedRecommendations(recommendation.selectedUniversities)
    } else {
      return generateMajorBasedRecommendations(recommendation.selectedMajors)
    }
  }

  // 基于目标学校生成推荐
  const generateUniversityBasedRecommendations = (universities: University[]): SubjectCombinationRecommendation[] => {
    if (universities.length === 0) return []

    const recommendations: SubjectCombinationRecommendation[] = []

    // 分析选中的大学特点
    const hasTopUniversity = universities.some(u => u.Is985 || u.Is211 || u.IsDualClass)
    const hasEngineeringFocused = universities.some(u =>
      u.CollegeCategory === '理工类' ||
      u.CollegeName.includes('理工') ||
      u.CollegeName.includes('科技')
    )

    if (hasTopUniversity && hasEngineeringFocused) {
      recommendations.push({
        combination: {
          firstChoice: '物理',
          secondChoices: ['化学', '生物'],
          name: '物理+化学+生物'
        },
        matchScore: 95,
        reasons: [
          '选中的院校工科专业实力强劲，物化生组合专业覆盖最全',
          '该组合是传统理科强势组合，符合顶尖工科院校要求',
          '有利于报考这些学校热门的工程、医学、理学等专业'
        ],
        advantages: ['专业选择最多', '理科基础扎实', '就业前景广阔'],
        risks: ['竞争激烈', '学习难度大', '需要较强的理科基础'],
        majorCoverage: 96.4,
        competitiveness: 'high'
      })
    }

    // 添加更多推荐...
    recommendations.push({
      combination: {
        firstChoice: '物理',
        secondChoices: ['化学', '地理'],
        name: '物理+化学+地理'
      },
      matchScore: 88,
      reasons: [
        '保持理科优势的同时降低学习难度',
        '地理学科相对简单，有助于提高总分',
        '适合冲刺选中院校的理工科专业'
      ],
      advantages: ['文理兼顾', '学习压力适中', '专业覆盖广'],
      risks: ['思维跨度较大', '部分顶尖工科专业可能受限'],
      majorCoverage: 95.8,
      competitiveness: 'medium'
    })

    return recommendations.sort((a, b) => b.matchScore - a.matchScore)
  }

  // 基于目标专业生成推荐
  const generateMajorBasedRecommendations = (majors: MajorTreeNode[]): SubjectCombinationRecommendation[] => {
    if (majors.length === 0) return []

    const recommendations: SubjectCombinationRecommendation[] = []

    // 分析选中的专业特点
    const hasComputerMajor = majors.some(m =>
      m.name.includes('计算机') ||
      m.name.includes('软件') ||
      m.name.includes('人工智能')
    )
    const hasMedicalMajor = majors.some(m =>
      m.name.includes('医学') ||
      m.name.includes('临床') ||
      m.name.includes('护理')
    )

    if (hasComputerMajor) {
      recommendations.push({
        combination: {
          firstChoice: '物理',
          secondChoices: ['化学', '生物'],
          name: '物理+化学+生物'
        },
        matchScore: 92,
        reasons: [
          '计算机专业通常要求物理作为必选科目',
          '化学和生物有助于理解算法和数据结构的底层逻辑',
          '该组合为计算机相关专业提供最佳基础'
        ],
        advantages: ['专业匹配度高', '基础扎实', '发展潜力大'],
        risks: ['竞争激烈', '学习难度较大'],
        majorCoverage: 96.4,
        competitiveness: 'high'
      })
    }

    if (hasMedicalMajor) {
      recommendations.push({
        combination: {
          firstChoice: '物理',
          secondChoices: ['化学', '生物'],
          name: '物理+化学+生物'
        },
        matchScore: 98,
        reasons: [
          '医学专业必须选择物理、化学、生物三科',
          '这是医学专业的标准配置，无其他选择',
          '为医学学习提供必要的科学基础'
        ],
        advantages: ['专业要求匹配', '科学基础扎实', '逻辑思维强'],
        risks: ['学习难度大', '竞争非常激烈'],
        majorCoverage: 96.4,
        competitiveness: 'high'
      })
    }

    return recommendations.sort((a, b) => b.matchScore - a.matchScore)
  }

  // 获取竞争程度颜色
  const getCompetitivenessColor = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-700'
      case 'medium': return 'bg-yellow-100 text-yellow-700'
      case 'high': return 'bg-red-100 text-red-700'
    }
  }

  // 获取竞争程度文本
  const getCompetitivenessText = (level: 'low' | 'medium' | 'high') => {
    switch (level) {
      case 'low': return '竞争较小'
      case 'medium': return '竞争适中'
      case 'high': return '竞争激烈'
    }
  }

  return (
    <div className="space-y-6">
      {/* 输入区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-orange-500" />
            智能分析设置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 类型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              分析类型
            </label>
            <div className="flex space-x-4">
              <Button
                variant={recommendation.type === 'university' ? 'default' : 'outline'}
                onClick={() => handleTypeChange('university')}
                className="flex items-center space-x-2"
              >
                <School className="w-4 h-4" />
                <span>目标学校</span>
              </Button>
              <Button
                variant={recommendation.type === 'major' ? 'default' : 'outline'}
                onClick={() => handleTypeChange('major')}
                className="flex items-center space-x-2"
              >
                <GraduationCap className="w-4 h-4" />
                <span>目标专业</span>
              </Button>
            </div>
          </div>

          {/* 搜索和选择区域 */}
          {recommendation.type === 'university' ? (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索并选择目标学校
              </label>
              <div className="relative">
                <Input
                  placeholder="请输入学校名称进行搜索，如：清华大学"
                  value={universitySearchKeyword}
                  onChange={(e) => handleUniversitySearch(e.target.value)}
                  className="pr-10"
                />
                {isSearchingUniversities && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-gray-400" />
                )}

                {/* 搜索结果下拉 */}
                {showUniversityResults && universitySearchResults.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {universitySearchResults.map((university) => (
                      <div
                        key={university.DataId}
                        onClick={() => handleUniversitySelect(university)}
                        className="px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{university.CollegeName}</div>
                            <div className="text-sm text-gray-500">
                              {university.Province} · {university.CollegeCategory}
                              {university.Is985 && <Badge className="ml-2 bg-red-100 text-red-700 text-xs">985</Badge>}
                              {university.Is211 && <Badge className="ml-1 bg-blue-100 text-blue-700 text-xs">211</Badge>}
                              {university.IsDualClass && <Badge className="ml-1 bg-green-100 text-green-700 text-xs">双一流</Badge>}
                            </div>
                          </div>
                          <Plus className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 已选择的学校 */}
              {recommendation.selectedUniversities.length > 0 && (
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    已选择的学校 ({recommendation.selectedUniversities.length})
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {recommendation.selectedUniversities.map((university) => (
                      <div
                        key={university.DataId}
                        className="flex items-center gap-2 bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm"
                      >
                        <span>{university.CollegeName}</span>
                        <button
                          onClick={() => handleRemoveUniversity(university.DataId)}
                          className="hover:bg-blue-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索并选择目标专业
              </label>
              <div className="relative">
                <Input
                  placeholder="请输入专业名称进行搜索，如：计算机科学与技术"
                  value={majorSearchKeyword}
                  onChange={(e) => handleMajorSearch(e.target.value)}
                  className="pr-10"
                />
                {isSearchingMajors && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-gray-400" />
                )}

                {/* 搜索结果下拉 */}
                {showMajorResults && majorSearchResults.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {majorSearchResults.map((major) => (
                      <div
                        key={major.id}
                        onClick={() => handleMajorSelect(major)}
                        className="px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{major.name}</div>
                            <div className="text-sm text-gray-500">
                              {major.disciplinaryCategory} · {major.educationLevel}
                            </div>
                          </div>
                          <Plus className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 已选择的专业 */}
              {recommendation.selectedMajors.length > 0 && (
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    已选择的专业 ({recommendation.selectedMajors.length})
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {recommendation.selectedMajors.map((major) => (
                      <div
                        key={major.id}
                        className="flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm"
                      >
                        <span>{major.name}</span>
                        <button
                          onClick={() => handleRemoveMajor(major.id)}
                          className="hover:bg-green-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 开始分析按钮 */}
          <div className="flex justify-center">
            <Button
              onClick={onAnalyze}
              disabled={
                (recommendation.type === 'university' && recommendation.selectedUniversities.length === 0) ||
                (recommendation.type === 'major' && recommendation.selectedMajors.length === 0) ||
                recommendation.isAnalyzing
              }
              className="bg-orange-500 hover:bg-orange-600 px-8"
            >
              {recommendation.isAnalyzing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  AI分析中...
                </>
              ) : (
                <>
                  <Brain className="w-4 h-4 mr-2" />
                  开始智能分析
                </>
              )}
            </Button>
          </div>

          {/* 快速选择建议 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              热门{recommendation.type === 'university' ? '学校' : '专业'}推荐
            </label>
            <div className="flex flex-wrap gap-2">
              {(recommendation.type === 'university' ? popularUniversities : popularMajors)
                .slice(0, 6)
                .map((item) => (
                <Button
                  key={item}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (recommendation.type === 'university') {
                      handleUniversitySearch(item)
                    } else {
                      handleMajorSearch(item)
                    }
                  }}
                  className="text-xs"
                >
                  <Search className="w-3 h-3 mr-1" />
                  {item}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 分析结果 */}
      {recommendation.isAnalyzing && (
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8 text-orange-500 animate-spin" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">AI正在分析中...</h3>
              <p className="text-gray-600">
                正在基于您的目标{recommendation.type === 'university' ? '学校' : '专业'}
                分析最适合的选科组合，请稍候...
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 推荐结果 */}
      {!recommendation.isAnalyzing && recommendation.recommendations.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-orange-500" />
              AI推荐结果
            </h3>
            <Badge className="bg-green-100 text-green-700">
              为您找到 {recommendation.recommendations.length} 个推荐组合
            </Badge>
          </div>

          {recommendation.recommendations.map((rec, index) => (
            <Card key={index} className="border-l-4 border-l-orange-500">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                      {index + 1}
                    </div>
                    {rec.combination.name}
                  </CardTitle>
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-orange-100 text-orange-700">
                      匹配度 {rec.matchScore}%
                    </Badge>
                    <Badge className={getCompetitivenessColor(rec.competitiveness)}>
                      {getCompetitivenessText(rec.competitiveness)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 科目组合展示 */}
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">首选：</span>
                    <Badge className="bg-blue-500 text-white">
                      {rec.combination.firstChoice}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-700">再选：</span>
                    {rec.combination.secondChoices.map((subject) => (
                      <Badge key={subject} className="bg-green-500 text-white">
                        {subject}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* 统计信息 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-4 h-4 text-blue-500" />
                    <span className="text-sm text-gray-600">
                      专业覆盖率: <span className="font-medium">{rec.majorCoverage}%</span>
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-gray-600">
                      匹配度: <span className="font-medium">{rec.matchScore}%</span>
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-purple-500" />
                    <span className="text-sm text-gray-600">
                      竞争: <span className="font-medium">{getCompetitivenessText(rec.competitiveness)}</span>
                    </span>
                  </div>
                </div>

                {/* 推荐理由 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center gap-1">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    推荐理由
                  </h4>
                  <ul className="space-y-1">
                    {rec.reasons.map((reason, idx) => (
                      <li key={idx} className="text-sm text-gray-600 flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 优势和风险 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center gap-1">
                      <Trophy className="w-4 h-4 text-yellow-500" />
                      主要优势
                    </h4>
                    <ul className="space-y-1">
                      {rec.advantages.map((advantage, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-start gap-2">
                          <span className="text-green-500 mt-1">✓</span>
                          {advantage}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center gap-1">
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                      注意事项
                    </h4>
                    <ul className="space-y-1">
                      {rec.risks.map((risk, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-start gap-2">
                          <span className="text-red-500 mt-1">!</span>
                          {risk}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 空状态 */}
      {!recommendation.isAnalyzing && recommendation.recommendations.length === 0 &&
       ((recommendation.type === 'university' && recommendation.selectedUniversities.length > 0) ||
        (recommendation.type === 'major' && recommendation.selectedMajors.length > 0)) && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无推荐结果</h3>
            <p className="text-gray-600">
              请点击"开始智能分析"按钮获取基于您选择的{recommendation.type === 'university' ? '学校' : '专业'}的选科推荐。
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

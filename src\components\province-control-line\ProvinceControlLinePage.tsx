import { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { ArrowLeft, Search, Download, Filter, TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { cn } from '../../lib/utils'
import { 
  fetchProvinceControlLines, 
  getProvinceList, 
  getYearList, 
  getCategoryList,
  type ProvinceControlLineData 
} from '../../services/provinceControlLineService'

interface ProvinceControlLinePageProps {
  onBack?: () => void
}

// 使用服务中定义的类型
type ControlLineData = ProvinceControlLineData

export function ProvinceControlLinePage({ onBack }: ProvinceControlLinePageProps) {
  const [selectedProvince, setSelectedProvince] = useState('安徽')
  const [selectedYear, setSelectedYear] = useState(2024)
  const [selectedCategory, setSelectedCategory] = useState('理科')
  const [controlLines, setControlLines] = useState<ControlLineData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 使用服务提供的数据
  const provinces = getProvinceList()
  const years = getYearList()
  const categories = getCategoryList()



  // 获取省控线数据
  const fetchControlLines = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetchProvinceControlLines({
        keyword: selectedProvince,
        year: selectedYear,
        category: selectedCategory
      })
      
      if (response.code === 200) {
        setControlLines(response.data)
      } else {
        setError(response.message || '获取数据失败')
      }
    } catch (err) {
      setError('获取数据失败，请稍后重试')
      console.error('Error fetching control lines:', err)
    } finally {
      setLoading(false)
    }
  }

  // 初始加载和条件变化时重新获取数据
  useEffect(() => {
    fetchControlLines()
  }, [selectedProvince, selectedYear, selectedCategory])

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />
      default:
        return <Minus className="w-4 h-4 text-gray-400" />
    }
  }

  const getTrendText = (trend?: string, value?: number) => {
    if (!trend || trend === 'same') return '持平'
    const direction = trend === 'up' ? '上升' : '下降'
    return value ? `${direction}${value}分` : direction
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回</span>
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">省控线查询</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                导出数据
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* 筛选条件 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Filter className="w-5 h-5 text-gray-500" />
            <h2 className="text-lg font-medium text-gray-900">筛选条件</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* 省份选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择省份
              </label>
              <select
                value={selectedProvince}
                onChange={(e) => setSelectedProvince(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {provinces.map(province => (
                  <option key={province} value={province}>{province}</option>
                ))}
              </select>
            </div>

            {/* 年份选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择年份
              </label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {years.map(year => (
                  <option key={year} value={year}>{year}年</option>
                ))}
              </select>
            </div>

            {/* 科类选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择科类
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 结果展示 */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b">
            <h2 className="text-lg font-medium text-gray-900">
              {selectedProvince} {selectedYear}年 {selectedCategory} 省控线
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              数据来源：各省教育考试院官方发布
            </p>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <p className="mt-2 text-gray-500">正在加载数据...</p>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <p className="text-red-500">{error}</p>
              <Button
                onClick={fetchControlLines}
                className="mt-4"
                variant="outline"
              >
                重新加载
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      批次
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      分数线
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      较去年
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      趋势
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {controlLines.map((line) => (
                    <tr key={line.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {line.batchType}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-lg font-bold text-blue-600">
                          {line.score}分
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={cn(
                          "text-sm font-medium",
                          line.trend === 'up' && "text-green-600",
                          line.trend === 'down' && "text-red-600",
                          line.trend === 'same' && "text-gray-500"
                        )}>
                          {getTrendText(line.trend, line.trendValue)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getTrendIcon(line.trend)}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 说明信息 */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">说明</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 省控线是各省份划定的最低录取控制分数线</li>
            <li>• 只有达到相应批次省控线的考生才能参与该批次的录取</li>
            <li>• 数据来源于各省教育考试院官方发布，仅供参考</li>
            <li>• 实际录取分数线可能高于省控线</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default ProvinceControlLinePage
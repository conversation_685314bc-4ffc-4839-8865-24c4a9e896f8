import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import type { University } from '../../types/university'
import {
  MapPin,
  Star,
  Award
} from 'lucide-react'

interface SimpleUniversityCardProps {
  university: University
  onClick?: () => void
  onViewDetail?: () => void
  className?: string
}

export function SimpleUniversityCard({ university, onClick, onViewDetail, className }: SimpleUniversityCardProps) {
  // 获取院校类型标签
  const getTypeLabels = () => {
    const labels = []
    if (university.Is985) labels.push({ text: '985', color: 'bg-red-100 text-red-800' })
    if (university.Is211) labels.push({ text: '211', color: 'bg-orange-100 text-orange-800' })
    if (university.IsDualClass) labels.push({ text: '双一流', color: 'bg-purple-100 text-purple-800' })
    return labels
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      '综合类': 'bg-green-100 text-green-800',
      '理工类': 'bg-blue-100 text-blue-800',
      '师范类': 'bg-yellow-100 text-yellow-800',
      '医药类': 'bg-red-100 text-red-800',
      '财经类': 'bg-purple-100 text-purple-800',
      '艺术类': 'bg-pink-100 text-pink-800',
      '农林类': 'bg-emerald-100 text-emerald-800',
      '政法类': 'bg-indigo-100 text-indigo-800',
      '体育类': 'bg-orange-100 text-orange-800',
      '民族类': 'bg-cyan-100 text-cyan-800',
      '军事类': 'bg-slate-100 text-slate-800',
      '语言类': 'bg-teal-100 text-teal-800',
      '其它': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['其它']
  }

  return (
    <Card
      className={cn(
        "group university-card hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-l-blue-500 hover:border-l-blue-600 p-4",
        className
      )}
      onClick={() => {
        onClick?.()
        onViewDetail?.()
      }}
    >
      {/* 改为两行布局以避免溢出 */}
      <div className="space-y-2">
        {/* 第一行：学校名称和所有标签 */}
        <div className="flex items-center gap-3 flex-1 min-w-0 mb-2">
          {university.CoverImage && (
            <img
              src={university.CoverImage}
              alt={`${university.CollegeName}校徽`}
              className="w-8 h-8 rounded-full object-cover flex-shrink-0"
            />
          )}

          <div className="flex items-center gap-2 flex-1 min-w-0">
            <h3 className="text-base font-bold text-gray-900 group-hover:text-blue-600 transition-colors truncate">
              {university.CollegeName}
            </h3>
            {/* 985、211标签紧跟学校名称 */}
            {getTypeLabels().map((label, index) => (
              <span key={index} className={cn("px-1.5 py-0.5 rounded text-xs font-medium flex-shrink-0", label.color)}>
                {label.text}
              </span>
            ))}
            {/* 院校类别标签也放在名称后方 */}
            <span className={cn("px-1.5 py-0.5 rounded text-xs font-medium flex-shrink-0", getCategoryColor(university.CollegeCategory))}>
              {university.CollegeCategory || '其它'}
            </span>
            {university.Ranking && university.Ranking > 0 && (
              <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full text-xs font-medium flex-shrink-0">
                <Star className="w-3 h-3" />
                {university.Ranking}
              </div>
            )}
          </div>
        </div>

        {/* 第二行：地区信息 */}
        <div className="flex items-center gap-2 text-xs text-gray-600">
          <MapPin className="w-3 h-3 flex-shrink-0" />
          <span className="truncate">
            {university.Province && university.City
              ? `${university.Province} · ${university.City}`
              : university.Province || university.City || '位置信息待完善'
            }
          </span>
          <span className="text-gray-400">|</span>
          <span className="truncate">{university.CollegeProperty || '性质待完善'}</span>
        </div>
      </div>
    </Card>
  )
}

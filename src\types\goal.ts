// 用户目标相关类型定义

// 目标类型
export type GoalType = 'university' | 'major'

// 大学目标
export interface UniversityGoal {
  type: 'university'
  universityId: string
  universityName: string
  province: string
  is985?: boolean
  is211?: boolean
  isDualClass?: boolean
  category?: string
  targetScore?: number // 预估录取分数
  targetRanking?: number // 预估录取位次
}

// 专业目标
export interface MajorGoal {
  type: 'major'
  universityId: string
  universityName: string
  majorId: string
  majorName: string
  majorCode?: string
  category?: string
  targetScore?: number // 预估录取分数
  targetRanking?: number // 预估录取位次
}

// 用户目标
export type UserGoal = UniversityGoal | MajorGoal

// 月考分数记录
export interface ExamScore {
  id: string
  date: string // ISO日期字符串
  examName: string // 考试名称，如"第一次月考"
  totalScore: number // 总分
  subjects: {
    [subject: string]: number // 各科分数
  }
  ranking?: number // 年级排名
  classRanking?: number // 班级排名
  notes?: string // 备注
  createdAt: string
  updatedAt: string
}

// 目标分析结果
export interface GoalAnalysis {
  goalId: string
  currentScore: number
  targetScore: number
  scoreDifference: number // 分数差距，正数表示还需提高，负数表示已达标
  isAchieved: boolean // 是否已达目标
  progress: number // 进度百分比 (0-100)
  trend: 'improving' | 'stable' | 'declining' // 成绩趋势
  recommendation: string // 建议
  nextMilestone?: {
    score: number
    description: string
  }
}

// 目标设置请求
export interface SetGoalRequest {
  goal: UserGoal
  userId: string
}

// 分数录入请求
export interface AddScoreRequest {
  examScore: Omit<ExamScore, 'id' | 'createdAt' | 'updatedAt'>
  userId: string
}

// 目标列表响应
export interface GoalListResponse {
  goals: UserGoal[]
  total: number
}

// 分数历史响应
export interface ScoreHistoryResponse {
  scores: ExamScore[]
  total: number
  analysis?: {
    averageScore: number
    highestScore: number
    lowestScore: number
    improvementRate: number // 提升幅度
  }
}

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { 
  ArrowLeft, 
  Send, 
  Bot, 
  User, 
  Loader2,
  MessageCircle,
  Sparkles,
  RefreshCw,
  MessageSquare,
  Zap,
  Settings,
  Trash2,
  X
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { AnimatedShinyText } from '../magicui/animated-shiny-text'
import { DotPattern } from '../magicui/dot-pattern'
import { aiService } from '../../services/aiService'
import { StreamingDemo } from './StreamingDemo'
import { MarkdownRenderer } from '../ui/markdown-renderer'
import { Card, CardContent } from '../ui/card'
import { Message } from '@/types/ai'

interface AiChatPageProps {
  onBack: () => void
}

export function AiChatPage({ onBack }: AiChatPageProps) {
  console.log('AiChatPage组件渲染')
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [apiKeyValid, setApiKeyValid] = useState<boolean | null>(null)
  const [conversationId, setConversationId] = useState<string | null>(null)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const [showDemo, setShowDemo] = useState(false)
  const [initialized, setInitialized] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 检查组件是否已初始化
  useEffect(() => {
    console.log('组件挂载检查，initialized:', initialized)
    if (!initialized) {
      console.log('组件未初始化，标记为已初始化')
      setInitialized(true)
    }
  }, [])

  // 检查API密钥有效性并创建对话
  useEffect(() => {
    // 只有在组件标记为已初始化后才执行
    if (!initialized) {
      console.log('组件尚未初始化，跳过API检查')
      return
    }

    const initializeChat = async () => {
      console.log('开始初始化聊天...')
      try {
        // 验证API密钥并获取对话ID
        console.log('验证API密钥...')
        const { isValid, conversationId } = await aiService.validateApiKey()
        console.log('API密钥验证结果:', isValid)
        setApiKeyValid(isValid)

        if (isValid && conversationId) {
          // 使用验证过程中创建的对话ID
          console.log('使用验证过程中创建的对话ID:', conversationId)
          setConversationId(conversationId)

          // 自动创建欢迎消息
          console.log('添加欢迎消息...')
          setMessages([
            {
              id: '1',
              role: 'assistant',
              content: '**欢迎使用智能问答助手！** 🎓\n\n我是您的高考志愿填报专业顾问，可以为您提供：\n\n• **专业选择指导** - 根据兴趣和能力推荐适合的专业\n• **院校信息查询** - 详细的大学信息和录取要求\n• **志愿填报策略** - 平行志愿、梯度设置等技巧\n• **就业前景分析** - 各专业的发展趋势和薪资水平\n• **政策解读** - 最新的高考政策和录取规则\n\n请直接在下方输入您的问题，我会为您提供专业、详细的解答！',
              timestamp: new Date(),
              animate: true
            }
          ])
          console.log('欢迎消息添加完成')
        } else {
          console.log('API密钥无效，无法创建对话')
        }
      } catch (error) {
        console.error('初始化聊天失败:', error)
        setApiKeyValid(false)
      }
    }

    // 立即执行初始化
    console.log('执行聊天初始化...')
    initializeChat()
  }, [initialized]) // 依赖initialized状态，确保只在组件初始化后执行一次

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || apiKeyValid === false) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      // 创建一个临时的助手消息用于流式更新
      const assistantMessageId = (Date.now() + 1).toString()
      const assistantMessage: Message = {
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date()
      }

      // 添加空的助手消息
      setMessages(prev => [...prev, assistantMessage])
      setStreamingMessageId(assistantMessageId)

      // 调用百度AI服务，支持流式更新
      const aiResponse = await aiService.askQuestion(
        userMessage.content,
        conversationId || undefined,
        (streamText: string) => {
          // 实时更新消息内容
          setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId
              ? { ...msg, content: streamText }
              : msg
          ))
        }
      )

      // 确保最终内容正确
      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessageId
          ? { ...msg, content: aiResponse }
          : msg
      ))
    } catch (error) {
      console.error('AI API调用失败:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '抱歉，服务暂时不可用。请稍后再试或联系技术支持。',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
      setStreamingMessageId(null)
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 测试API配置
  const handleTestConfig = async () => {
    console.log('开始测试百度AppBuilder配置...')
    console.log('API Key:', import.meta.env.VITE_BAIDU_API_KEY ? '已配置' : '未配置')
    console.log('App ID:', import.meta.env.VITE_BAIDU_APP_ID ? '已配置' : '未配置')

    try {
      const { isValid, conversationId } = await aiService.validateApiKey()
      console.log('API验证结果:', isValid ? '成功' : '失败')

      if (isValid && conversationId) {
        console.log('验证成功，创建的对话ID:', conversationId)
      }
    } catch (error) {
      console.error('测试配置失败:', error)
    }
  }

  // 清空对话
  const handleClearChat = async () => {
    console.log('开始清空对话...')
    try {
      // 创建新对话
      console.log('创建新对话...')
      const newConversationId = await aiService.createConversation()
      console.log('新对话创建成功:', newConversationId)
      setConversationId(newConversationId)

      console.log('重置消息列表...')
      setMessages([{
          id: '1',
          role: 'assistant',
          content: '**欢迎使用智能问答助手！** 🎓\n\n我是您的高考志愿填报专业顾问，可以为您提供：\n\n• **专业选择指导** - 根据兴趣和能力推荐适合的专业\n• **院校信息查询** - 详细的大学信息和录取要求\n• **志愿填报策略** - 平行志愿、梯度设置等技巧\n• **就业前景分析** - 各专业的发展趋势和薪资水平\n• **政策解读** - 最新的高考政策和录取规则\n\n请直接在下方输入您的问题，我会为您提供专业、详细的解答！',
          timestamp: new Date(),
          animate: true
        }])
      console.log('对话清空完成')
    } catch (error) {
      console.error('创建新对话失败:', error)
    }
  }

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-blue-50/80 via-white/80 to-purple-50/80">
      {/* 背景装饰 */}
      <DotPattern
        className="absolute inset-0 opacity-30"
        width={20}
        height={20}
        cx={1}
        cy={1}
        cr={1}
        glow={true}
      />

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* 顶部导航栏 */}
        <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50 shadow-sm">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="flex items-center gap-2 hover:bg-indigo-50 transition-colors duration-200"
                >
                  <ArrowLeft className="w-4 h-4 text-indigo-600" />
                  返回
                </Button>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-md">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <AnimatedShinyText className="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
                      智能问答
                    </AnimatedShinyText>
                    <p className="text-sm text-gray-500">AI助手为您答疑解惑</p>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {/* API状态指示器 */}
                <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-gray-50 border border-gray-200">
                  <div className={cn(
                    "w-2.5 h-2.5 rounded-full",
                    apiKeyValid === true ? "bg-green-500 animate-pulse" :
                    apiKeyValid === false ? "bg-red-500" : "bg-yellow-500 animate-pulse"
                  )} />
                  <span className={cn(
                    "text-xs",
                    apiKeyValid === true ? "text-green-600" :
                    apiKeyValid === false ? "text-red-600" : "text-amber-600"
                  )}>
                    {apiKeyValid === true ? "服务正常" :
                     apiKeyValid === false ? "服务异常" : "检查中..."}
                  </span>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDemo(true)}
                  className="flex items-center gap-2 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200"
                >
                  <Sparkles className="w-4 h-4 text-amber-500" />
                  流式演示
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTestConfig}
                  className="flex items-center gap-2 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200"
                >
                  <Settings className="w-4 h-4 text-blue-500" />
                  测试配置
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearChat}
                  className="flex items-center gap-2 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200"
                >
                  <RefreshCw className="w-4 h-4 text-red-400" />
                  清空对话
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* 聊天区域 */}
        <div className="flex-1 container mx-auto px-4 py-6 flex flex-col max-w-4xl">
          {/* API配置提示 */}
          {apiKeyValid === false && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg shadow-sm animate-fadeIn">
              <div className="flex items-start">
                <div className="mr-3 flex-shrink-0 bg-yellow-100 rounded-full p-2">
                  <Sparkles className="w-5 h-5 text-yellow-600" />
                </div>
                <div>
                  <h3 className="font-medium text-yellow-800">API配置提示</h3>
                  <p className="text-sm text-yellow-700 mt-2">
                    请配置有效的百度AppBuilder API密钥和应用ID以使用智能问答功能。
                    <br />
                    请在项目根目录创建 .env 文件并添加：
                    <br />
                    VITE_BAIDU_API_KEY=your_api_key_here
                    <br />
                    VITE_BAIDU_APP_ID=your_app_id_here
                  </p>
                  <div className="mt-3">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="text-xs border-yellow-200 text-yellow-700 hover:bg-yellow-100 hover:text-yellow-800"
                      onClick={() => window.location.href = '/settings'}
                    >
                      <Settings className="w-3.5 h-3.5 mr-1" />
                      前往设置
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 消息列表 */}
          <div className="flex-1 overflow-y-auto space-y-4 mb-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.role === 'user' ? 'justify-end' : 'justify-start',
                  message.animate ? 'animate-float' : 'animate-fadeIn'
                )}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {message.role === 'assistant' && (
                  <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="w-5 h-5 text-white" />
                  </div>
                )}
                
                <Card
                  className={cn(
                    "max-w-[70%] relative overflow-hidden transition-all duration-300",
                    message.role === 'user'
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-400 hover:shadow-blue-100/50'
                      : 'bg-white hover:shadow-lg hover:border-indigo-200 border-gray-200'
                  )}
                >
                  <CardContent className="p-4">
                    {message.role === 'assistant' ? (
                      <div className="prose prose-sm max-w-none">
                        <MarkdownRenderer 
                          content={message.content} 
                          isStreaming={streamingMessageId === message.id}
                        />
                        {/* 流式输入时显示光标 */}
                        {streamingMessageId === message.id && (
                          <span className="inline-block w-2 h-4 bg-indigo-500 ml-1 animate-pulse" />
                        )}
                      </div>
                    ) : (
                      <div className="text-sm leading-relaxed whitespace-pre-wrap">
                        {message.content}
                      </div>
                    )}
                    <p className={cn(
                      "text-xs mt-3 flex items-center",
                      message.role === 'user' ? 'text-blue-100' : 'text-gray-400'
                    )}>
                      <span>{formatTime(message.timestamp)}</span>
                      {/* 流式状态指示 */}
                      {streamingMessageId === message.id && (
                        <span className="ml-2 text-indigo-500 flex items-center gap-1">
                          <Sparkles className="w-3 h-3 inline animate-spin" />
                          <span>正在生成...</span>
                        </span>
                      )}
                    </p>
                  </CardContent>
                </Card>

                {message.role === 'user' && (
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="w-5 h-5 text-white" />
                  </div>
                )}
              </div>
            ))}
            
            {/* 加载指示器 - 只在初始加载时显示 */}
            {isLoading && !streamingMessageId && (
              <div className="flex gap-3 justify-start">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 shadow-sm">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin text-indigo-500" />
                    <span className="text-sm text-gray-500">正在连接AI服务...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <Card className="border-indigo-100 shadow-md">
            <CardContent className="p-4">
              <div className="flex gap-3">
                <div className="flex-1 relative group">
                  <textarea
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={apiKeyValid === false ? "请先配置API密钥..." : "请输入您的问题..."}
                    className="w-full resize-none border-0 bg-transparent focus:ring-0 focus:outline-none text-sm leading-relaxed min-h-[40px] max-h-[120px] py-2 pr-10 transition-all duration-300"
                    rows={1}
                    disabled={isLoading || apiKeyValid === false || streamingMessageId !== null}
                  />
                  {inputValue.trim() && (
                    <button 
                      onClick={() => setInputValue('')}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading || apiKeyValid === false || streamingMessageId !== null}
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white px-4 py-2 rounded-xl flex items-center gap-2 self-end disabled:opacity-50 shadow-md hover:shadow-indigo-200/50 transition-all duration-300"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
              
              {/* 快捷问题 */}
              <div className="mt-3 flex flex-wrap gap-2">
                {aiService.getCommonQuestions().slice(0, 4).map((question, index) => (
                  <button
                    key={index}
                    onClick={() => setInputValue(question)}
                    className="text-xs px-3 py-1.5 bg-gray-50 hover:bg-gray-100 rounded-full text-gray-600 transition-all duration-200 flex items-center gap-1 disabled:opacity-50 border border-gray-200 hover:shadow-sm hover:border-indigo-200"
                    disabled={isLoading || apiKeyValid === false || streamingMessageId !== null}
                  >
                    <Sparkles className="w-3 h-3" />
                    {question}
                  </button>
                ))}
              </div>

            {/* 流式状态提示 */}
            {streamingMessageId && (
              <div className="mt-3 text-xs text-indigo-600 flex items-center gap-2">
                <Sparkles className="w-3 h-3 animate-spin" />
                AI正在实时生成回答，请稍候...
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 流式演示弹窗 */}
      {showDemo && (
        <StreamingDemo onClose={() => setShowDemo(false)} />
      )}
    </div>
  )
}

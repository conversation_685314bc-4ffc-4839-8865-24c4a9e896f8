import { useState, useEffect } from "react"
import { ArrowLeft, GraduationCap, Calendar, Eye, Loader2, AlertCircle, BookOpen, TrendingUp, Users } from "lucide-react"
import { Button } from "../ui/button"
import { 
  getPopularMajorDetail, 
  formatTimestamp, 
  getCategoryInfo,
  DETAIL_VIEW_OPTIONS,
  type PopularMajorItem,
  type PopularMajorDetailItem 
} from "../../services/popularMajorsApi"

interface PopularMajorDetailPageProps {
  onBack: () => void
  selectedMajor: PopularMajorItem | null
}

export function PopularMajorDetailPage({ onBack, selectedMajor }: PopularMajorDetailPageProps) {
  const [detailData, setDetailData] = useState<PopularMajorDetailItem | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedView, setSelectedView] = useState('overview')

  // 加载详情数据
  const loadDetailData = async (questionId: number, version: string = '', dsCus: string = '') => {
    setLoading(true)
    setError(null)

    try {
      const response = await getPopularMajorDetail(questionId, version, dsCus)
      if (response.data.list.length > 0) {
        setDetailData(response.data.list[0])
      } else {
        setError('未找到详细内容')
      }
    } catch (err) {
      console.error('加载详情失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 当选中的专业或视图改变时重新加载数据
  useEffect(() => {
    if (selectedMajor) {
      const viewOption = DETAIL_VIEW_OPTIONS.find(option => option.id === selectedView)
      if (viewOption) {
        loadDetailData(selectedMajor.id, viewOption.version, viewOption.dsCus)
      }
    }
  }, [selectedMajor, selectedView])

  // 处理视图切换
  const handleViewChange = (viewId: string) => {
    setSelectedView(viewId)
  }

  if (!selectedMajor) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">未选择专业</h3>
          <p className="text-gray-600 mb-4">请先选择一个专业查看详情</p>
          <Button onClick={onBack}>返回列表</Button>
        </div>
      </div>
    )
  }

  const categoryInfo = getCategoryInfo(selectedMajor.type)

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回列表</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <GraduationCap className="w-6 h-6 text-orange-600" />
                <h1 className="text-xl font-bold text-gray-800">热门专业详情</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 专业标题卡片 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <span className={`px-3 py-1 text-sm font-medium rounded-full ${categoryInfo.bgColor} ${categoryInfo.color}`}>
                  {categoryInfo.name}
                </span>
                {selectedMajor.createTime && (
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <Calendar className="w-4 h-4" />
                    <span>{formatTimestamp(selectedMajor.createTime)}</span>
                  </div>
                )}
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-3">
                {selectedMajor.question}
              </h1>
              <p className="text-lg text-gray-600">
                {selectedMajor.ds}
              </p>
            </div>
          </div>
        </div>

        {/* 视图选择器 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">查看内容</h3>
          <div className="flex flex-wrap gap-3">
            {DETAIL_VIEW_OPTIONS.map((option) => (
              <button
                key={option.id}
                onClick={() => handleViewChange(option.id)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                  selectedView === option.id
                    ? 'bg-orange-100 text-orange-700 border-2 border-orange-200'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border-2 border-transparent'
                }`}
              >
                {option.id === 'overview' && <BookOpen className="w-4 h-4" />}
                {option.id === 'score-segments' && <TrendingUp className="w-4 h-4" />}
                {option.id === 'career-prospects' && <Users className="w-4 h-4" />}
                <span>{option.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 详细内容 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-orange-600 mr-3" />
              <span className="text-gray-600">加载详细内容中...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button 
                onClick={() => {
                  const viewOption = DETAIL_VIEW_OPTIONS.find(option => option.id === selectedView)
                  if (selectedMajor && viewOption) {
                    loadDetailData(selectedMajor.id, viewOption.version, viewOption.dsCus)
                  }
                }}
                variant="outline"
              >
                重试
              </Button>
            </div>
          ) : detailData ? (
            <div className="prose prose-lg max-w-none">
              <div 
                className="text-gray-800 leading-relaxed [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h2]:text-xl [&>h2]:font-semibold [&>h2]:mb-3 [&>h3]:text-lg [&>h3]:font-medium [&>h3]:mb-2 [&>h4]:text-base [&>h4]:font-medium [&>h4]:mb-2 [&>p]:mb-4 [&>ul]:mb-4 [&>ul]:pl-6 [&>ol]:mb-4 [&>ol]:pl-6 [&>li]:mb-2 [&>li]:list-disc [&>ol>li]:list-decimal [&>hr]:my-6 [&>hr]:border-gray-300 [&>strong]:font-semibold [&>em]:italic [&>table]:w-full [&>table]:border-collapse [&>table]:border [&>table]:border-gray-300 [&>table]:mb-4 [&>th]:border [&>th]:border-gray-300 [&>th]:bg-gray-100 [&>th]:p-2 [&>th]:text-left [&>td]:border [&>td]:border-gray-300 [&>td]:p-2 [&>a]:text-blue-600 [&>a]:underline [&>a:hover]:text-blue-800"
                dangerouslySetInnerHTML={{ __html: detailData.content }}
              />
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">暂无详细内容</h3>
              <p className="text-gray-600">该专业的详细内容正在完善中</p>
            </div>
          )}
        </div>

        {/* 相关推荐 */}
        <div className="mt-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <GraduationCap className="w-5 h-5 mr-2 text-orange-600" />
            相关推荐
          </h3>
          <div className="text-gray-600">
            <p className="mb-2">• 查看更多 <span className="font-medium">{categoryInfo.name}</span> 相关专业</p>
            <p className="mb-2">• 了解专业就业前景和薪资待遇</p>
            <p>• 查看院校专业录取分数线</p>
          </div>
        </div>
      </div>
    </div>
  )
}

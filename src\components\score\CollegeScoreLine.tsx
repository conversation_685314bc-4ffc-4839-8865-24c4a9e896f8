import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { Loader2, Search, School, Calendar, TrendingUp, Award, ChevronLeft, ChevronRight } from 'lucide-react'
import {
  getCollegeScoreLine,
  PROVINCES,
  SUBJECT_TYPES,
  ADMISSION_BATCHES,
  getAvailableSubjectSelections
} from '../../services/scoreLineApiSimple'
import type {
  CollegeScoreLineFilters,
  CollegeScoreLineData
} from '../../services/scoreLineApiSimple'

interface CollegeScoreLineProps {
  className?: string
}

export function CollegeScoreLine({ className }: CollegeScoreLineProps) {
  const [filters, setFilters] = useState<CollegeScoreLineFilters>({
    searchtype: 'PROVINCENAME',
    keyword: '安徽',
    pageindex: 1,
    pagesize: 10,
    year: 2024,
    type: '物理类'
  })

  const [data, setData] = useState<CollegeScoreLineData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)

  // 根据当前选择的省份和年份获取可用的科目类型
  const availableSubjects = getAvailableSubjectSelections(
    filters.searchtype === 'PROVINCENAME' ? filters.keyword : '安徽',
    filters.year?.toString()
  )

  // 查询高校录取分数线
  const handleSearch = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await getCollegeScoreLine(filters)
      
      if (response.DataStatus.StatusCode === 100) {
        setData(response.Data || [])
        setTotalCount(response.DataStatus.DataTotalCount || 0)
        console.log('✅ 高校录取分数线查询成功，获取到', response.Data?.length || 0, '条数据')
      } else {
        throw new Error(response.DataStatus.StatusDescription || '查询失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '查询失败'
      setError(errorMessage)
      console.error('❌ 高校录取分数线查询失败:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // 重置筛选条件
  const handleReset = () => {
    setFilters({
      searchtype: 'PROVINCENAME',
      keyword: '',
      pageindex: 1,
      pagesize: 10
    })
    setData([])
    setError(null)
  }

  // 分页处理
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, pageindex: newPage }))
  }

  // 计算总页数
  const totalPages = Math.ceil(totalCount / (filters.pagesize || 10))

  // 获取学校类型颜色
  const getSchoolTypeColor = (is985: boolean, is211: boolean, isDualClass: boolean) => {
    if (is985) return 'bg-red-100 text-red-800'
    if (is211) return 'bg-orange-100 text-orange-800'
    if (isDualClass) return 'bg-blue-100 text-blue-800'
    return 'bg-gray-100 text-gray-800'
  }

  // 获取学校类型标签
  const getSchoolTypeLabel = (is985: boolean, is211: boolean, isDualClass: boolean) => {
    if (is985) return '985'
    if (is211) return '211'
    if (isDualClass) return '双一流'
    return '普通'
  }

  return (
    <div className={className}>
      {/* 查询表单 */}
      <Card className="p-6 mb-6">
        <div className="flex items-center gap-2 mb-4">
          <School className="w-5 h-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">高校录取分数线查询</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* 查询类型 */}
          <div>
            <Label htmlFor="searchtype" className="text-sm font-medium text-gray-700">查询类型</Label>
            <Select
              value={filters.searchtype}
              onValueChange={(value: 'PROVINCENAME' | 'COLLEGENAME') => 
                setFilters(prev => ({ ...prev, searchtype: value, keyword: '' }))
              }
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PROVINCENAME">按省份查询</SelectItem>
                <SelectItem value="COLLEGENAME">按高校查询</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 关键词输入 */}
          <div>
            <Label htmlFor="keyword" className="text-sm font-medium text-gray-700">
              {filters.searchtype === 'PROVINCENAME' ? '省份' : '高校名称'}
            </Label>
            {filters.searchtype === 'PROVINCENAME' ? (
              <Select
                value={filters.keyword}
                onValueChange={(value) => setFilters(prev => ({ ...prev, keyword: value }))}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="选择省份" />
                </SelectTrigger>
                <SelectContent>
                  {PROVINCES.map(province => (
                    <SelectItem key={province} value={province}>{province}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <Input
                value={filters.keyword}
                onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
                placeholder="输入高校名称，支持模糊查询"
                className="mt-1"
              />
            )}
          </div>

          {/* 年份选择 */}
          <div>
            <Label htmlFor="year" className="text-sm font-medium text-gray-700">年份</Label>
            <Select
              value={filters.year?.toString() || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, year: value ? parseInt(value) : undefined }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择年份" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部年份</SelectItem>
                {Array.from({ length: 11 }, (_, i) => 2025 - i).map(year => (
                  <SelectItem key={year} value={year.toString()}>{year}年</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 科目类型选择 */}
          <div>
            <Label htmlFor="type" className="text-sm font-medium text-gray-700">科目类型</Label>
            <Select
              value={filters.type || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择科目类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                {availableSubjects.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 高级筛选 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {/* 最低分筛选 */}
          <div>
            <Label htmlFor="min" className="text-sm font-medium text-gray-700">最低分筛选</Label>
            <Input
              type="number"
              value={filters.min || ''}
              onChange={(e) => setFilters(prev => ({ ...prev, min: e.target.value ? parseInt(e.target.value) : undefined }))}
              placeholder="筛选最低分小于等于此值的数据"
              className="mt-1"
            />
          </div>

          {/* 录取批次 */}
          <div>
            <Label htmlFor="batchname" className="text-sm font-medium text-gray-700">录取批次</Label>
            <Select
              value={filters.batchname || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, batchname: value }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择录取批次" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部批次</SelectItem>
                {ADMISSION_BATCHES.map(batch => (
                  <SelectItem key={batch} value={batch}>{batch}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 每页条数 */}
          <div>
            <Label htmlFor="pagesize" className="text-sm font-medium text-gray-700">每页条数</Label>
            <Select
              value={filters.pagesize?.toString() || '10'}
              onValueChange={(value) => setFilters(prev => ({ ...prev, pagesize: parseInt(value), pageindex: 1 }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5条</SelectItem>
                <SelectItem value="10">10条</SelectItem>
                <SelectItem value="15">15条</SelectItem>
                <SelectItem value="20">20条</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-2">
          <Button
            onClick={handleSearch}
            disabled={isLoading || !filters.keyword}
            className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Search className="w-4 h-4 mr-2" />
            )}
            查询
          </Button>
          <Button
            onClick={handleReset}
            variant="outline"
          >
            重置
          </Button>
        </div>

        {/* 查询统计 */}
        {totalCount > 0 && (
          <div className="text-sm text-gray-600 mt-4">
            共找到 <span className="font-medium text-blue-600">{totalCount}</span> 条录取分数线数据
          </div>
        )}
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="p-4 mb-6 border-red-200 bg-red-50">
          <div className="text-red-800 text-sm">{error}</div>
        </Card>
      )}

      {/* 数据列表 */}
      {data.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Award className="w-5 h-5 text-orange-600" />
            <h3 className="text-lg font-semibold text-gray-900">录取分数线</h3>
          </div>
          
          <div className="space-y-4">
            {data.map((item, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium text-gray-900 text-lg">{item.CollegeName}</h4>
                    <Badge 
                      variant="outline" 
                      className={getSchoolTypeColor(item.Is985, item.Is211, item.IsDualClass)}
                    >
                      {getSchoolTypeLabel(item.Is985, item.Is211, item.IsDualClass)}
                    </Badge>
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      {item.TypeName}
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-500">
                    {item.Year}年 · {item.Province}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">最高分:</span>
                    <div className="font-bold text-red-600">{item.HighestScore || '-'}</div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">平均分:</span>
                    <div className="font-bold text-orange-600">{item.AverageScore || '-'}</div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">最低分:</span>
                    <div className="font-bold text-green-600">{item.LowestScore || '-'}</div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">最低位次:</span>
                    <div className="font-medium text-blue-600">{item.LowestRank || '-'}</div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">省控线:</span>
                    <div className="font-medium text-purple-600">{item.ProvincialControlLine || '-'}</div>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">录取批次:</span>
                    <div className="font-medium text-gray-800">{item.AdmissionBatch}</div>
                  </div>
                </div>
                
                {(item.SchoolInCity || item.EnrollmentType) && (
                  <div className="mt-3 pt-3 border-t border-gray-100 text-sm text-gray-600">
                    {item.SchoolInCity && <span>所在城市: {item.SchoolInCity}</span>}
                    {item.SchoolInCity && item.EnrollmentType && <span className="mx-2">·</span>}
                    {item.EnrollmentType && <span>招生类型: {item.EnrollmentType}</span>}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(filters.pageindex! - 1)}
                disabled={filters.pageindex === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                上一页
              </Button>
              
              <span className="text-sm text-gray-600">
                第 {filters.pageindex} 页，共 {totalPages} 页
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(filters.pageindex! + 1)}
                disabled={filters.pageindex === totalPages}
              >
                下一页
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* 空状态 */}
      {!isLoading && data.length === 0 && !error && (
        <Card className="p-8 text-center">
          <div className="text-gray-500 mb-2">暂无数据</div>
          <div className="text-sm text-gray-400">请调整查询条件后重试</div>
        </Card>
      )}
    </div>
  )
}

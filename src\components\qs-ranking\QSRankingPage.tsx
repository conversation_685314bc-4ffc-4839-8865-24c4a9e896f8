import { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { QSSearchForm } from './QSSearchForm'
import { QSRankingList } from './QSRankingList'
import { QSRankingStats } from './QSRankingStats'
import { qsRankingService } from '../../services/qsRankingService'
import type { 
  ProcessedQSUniversity, 
  QSRankingFilter, 
  QSRankingQuery 
} from '../../types/qs-ranking'
import { cn } from '../../lib/utils'
import {
  ArrowLeft,
  Globe,
  Trophy,
  TrendingUp,
  Info,
  Loader2,
  Languages
} from 'lucide-react'

interface QSRankingPageProps {
  onBack?: () => void
}

export function QSRankingPage({ onBack }: QSRankingPageProps) {
  const [universities, setUniversities] = useState<ProcessedQSUniversity[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)

  const [filter, setFilter] = useState<QSRankingFilter>({
    searchName: '',
    selectedCountry: '',
    selectedRegion: '',
    rankRange: { min: 1, max: 100 },
    showTopOnly: false
  })
  const [showChinese, setShowChinese] = useState(true)

  // Load initial data
  useEffect(() => {
    loadRankingData()
  }, [currentPage])

  // Apply filters when filter changes
  useEffect(() => {
    // 当筛选条件改变时，重置到第一页并重新加载数据
    setCurrentPage(1)
    loadRankingData()
  }, [filter])

  const loadRankingData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const query: QSRankingQuery = {
        pageIndex: currentPage,
        pageSize: pageSize,
        name: filter.searchName || undefined
      }

      const response = await qsRankingService.fetchQSRanking(query)
      const processedData = qsRankingService.processUniversityData(response.Data)



      setUniversities(processedData)
      setTotalCount(response.DataStatus.DataTotalCount)
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载数据失败')
      console.error('Failed to load QS ranking data:', err)
    } finally {
      setIsLoading(false)
    }
  }



  const handleSearch = () => {
    setCurrentPage(1)
    loadRankingData()
  }

  const handleFilterChange = (newFilter: Partial<QSRankingFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }))
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回</span>
              </Button>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Globe className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">全球QS大学排名</h1>
                  <p className="text-sm text-gray-600">QS World University Rankings</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Trophy className="w-4 h-4" />
                <span>权威排名</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <TrendingUp className="w-4 h-4" />
                <span>实时更新</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowChinese(!showChinese)}
                className="flex items-center space-x-2"
              >
                <Languages className="w-4 h-4" />
                <span>{showChinese ? '中文' : 'English'}</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Introduction */}
        <div className="mb-6 p-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl text-white">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <Globe className="w-6 h-6" />
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-2">QS世界大学排名</h2>
              <p className="text-blue-100 mb-4">
                QS世界大学排名是全球最权威的大学排名之一，基于学术声誉、雇主声誉、师生比例、研究引用率等多个维度进行综合评估。
              </p>
              <div className="flex flex-wrap gap-3">
                <div className="bg-white/20 px-3 py-1 rounded-full text-sm">学术声誉 40%</div>
                <div className="bg-white/20 px-3 py-1 rounded-full text-sm">师生比例 20%</div>
                <div className="bg-white/20 px-3 py-1 rounded-full text-sm">引用率 20%</div>
                <div className="bg-white/20 px-3 py-1 rounded-full text-sm">雇主声誉 10%</div>
                <div className="bg-white/20 px-3 py-1 rounded-full text-sm">国际化 10%</div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Search and Filters */}
          <div className="lg:col-span-1">
            <QSSearchForm
              filter={filter}
              onFilterChange={handleFilterChange}
              onSearch={handleSearch}
              isLoading={isLoading}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Statistics */}
            <QSRankingStats
              totalUniversities={totalCount}
              filteredCount={universities.length}
              topCountries={getTopCountries(universities)}
            />

            {/* Error State */}
            {error && (
              <Card className="p-6 border-red-200 bg-red-50">
                <div className="flex items-center gap-3 text-red-800">
                  <Info className="w-5 h-5" />
                  <div>
                    <p className="font-medium">加载失败</p>
                    <p className="text-sm text-red-600">{error}</p>
                  </div>
                </div>
              </Card>
            )}

            {/* Loading State */}
            {isLoading && (
              <Card className="p-8">
                <div className="flex items-center justify-center gap-3 text-gray-600">
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span>正在加载排名数据...</span>
                </div>
              </Card>
            )}

            {/* University List */}
            {!isLoading && !error && (
              <QSRankingList
                universities={universities}
                currentPage={currentPage}
                pageSize={pageSize}
                totalCount={totalCount}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper function to get top countries
function getTopCountries(universities: ProcessedQSUniversity[]): Array<{country: string, count: number}> {
  const countryCount = universities.reduce((acc, uni) => {
    // Use Chinese name if available, otherwise use English name
    const countryName = uni.countryZh !== uni.country ? uni.countryZh : uni.country
    acc[countryName] = (acc[countryName] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return Object.entries(countryCount)
    .map(([country, count]) => ({ country, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
}

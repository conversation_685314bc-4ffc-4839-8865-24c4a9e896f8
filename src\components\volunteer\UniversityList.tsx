import { useState, useMemo } from 'react'
import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import { UniversityCard } from './UniversityCard'
import type { VolunteerUniversity, VolunteerFilter } from '../../types/volunteer'
import {
  Search,
  SlidersHorizontal,
  ArrowUpDown,
  Filter,
  Grid3X3,
  List,
  TrendingUp,
  MapPin
} from 'lucide-react'

interface UniversityListProps {
  universities: VolunteerUniversity[]
  filters: VolunteerFilter
  selectedUniversities: string[]
  userScore?: number
  userRanking?: number
  onUniversitySelect: (university: VolunteerUniversity) => void
  onUniversityDetail: (university: VolunteerUniversity) => void
  onFiltersChange: (filters: VolunteerFilter) => void
  className?: string
}

type SortOption = 'ranking' | 'score' | 'name' | 'location' | 'probability'
type ViewMode = 'grid' | 'list'

export function UniversityList({
  universities,
  filters,
  selectedUniversities,
  userScore,
  userRanking,
  onUniversitySelect,
  onUniversityDetail,
  onFiltersChange,
  className
}: UniversityListProps) {
  const [sortBy, setSortBy] = useState<SortOption>('ranking')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [searchKeyword, setSearchKeyword] = useState('')

  // 筛选和排序逻辑
  const filteredAndSortedUniversities = useMemo(() => {
    let filtered = universities.filter(university => {
      // 关键词搜索
      if (searchKeyword) {
        const keyword = searchKeyword.toLowerCase()
        const matchName = university.name.toLowerCase().includes(keyword)
        const matchLocation = university.location.province.toLowerCase().includes(keyword) ||
                             university.location.city.toLowerCase().includes(keyword)
        const matchMajors = university.keyMajors.some(major => 
          major.toLowerCase().includes(keyword)
        )
        if (!matchName && !matchLocation && !matchMajors) return false
      }

      // 地区筛选
      if (filters.provinces.length > 0 && !filters.provinces.includes(university.location.province)) {
        return false
      }

      // 类型筛选
      if (filters.types.length > 0 && !filters.types.includes(university.type)) {
        return false
      }

      // 类别筛选
      if (filters.categories.length > 0 && !filters.categories.includes(university.category)) {
        return false
      }

      // 层次筛选
      if (filters.levels.length > 0 && !filters.levels.includes(university.level)) {
        return false
      }

      // 公办筛选
      if (filters.isPublicOnly && !university.isPublic) {
        return false
      }

      // 分数范围筛选
      if (university.scores.length > 0) {
        const latestScore = university.scores[0]
        if (latestScore.minScore < filters.scoreRange.min || 
            latestScore.minScore > filters.scoreRange.max) {
          return false
        }
      }

      // 位次范围筛选
      if (university.scores.length > 0) {
        const latestScore = university.scores[0]
        if (latestScore.ranking < filters.rankingRange.min || 
            latestScore.ranking > filters.rankingRange.max) {
          return false
        }
      }

      // 特色筛选
      if (filters.features.length > 0) {
        const hasFeature = filters.features.some(feature => 
          university.features.includes(feature)
        )
        if (!hasFeature) return false
      }

      return true
    })

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'ranking':
          comparison = (a.ranking?.national || 999) - (b.ranking?.national || 999)
          break
        case 'score':
          const aScore = a.scores[0]?.minScore || 0
          const bScore = b.scores[0]?.minScore || 0
          comparison = bScore - aScore
          break
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'location':
          comparison = a.location.province.localeCompare(b.location.province)
          break
        case 'probability':
          if (userScore && userRanking) {
            const aProbability = calculateProbability(a, userScore, userRanking)
            const bProbability = calculateProbability(b, userScore, userRanking)
            comparison = bProbability - aProbability
          }
          break
      }

      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filtered
  }, [universities, filters, searchKeyword, sortBy, sortOrder, userScore, userRanking])

  // 计算录取概率
  const calculateProbability = (university: VolunteerUniversity, score: number, ranking: number): number => {
    if (!university.scores.length) return 0
    
    const latestScore = university.scores[0]
    const scoreDiff = score - latestScore.minScore
    const rankingDiff = ranking - latestScore.ranking

    if (scoreDiff >= 20 && rankingDiff <= -1000) return 85
    if (scoreDiff >= 10 && rankingDiff <= -500) return 75
    if (scoreDiff >= 0 && rankingDiff <= 0) return 60
    if (scoreDiff >= -10 && rankingDiff <= 500) return 45
    return 25
  }

  const handleSortChange = (newSortBy: SortOption) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(newSortBy)
      setSortOrder('asc')
    }
  }

  const handleSearchChange = (value: string) => {
    setSearchKeyword(value)
    onFiltersChange({
      ...filters,
      searchKeyword: value
    })
  }

  const getSortIcon = (option: SortOption) => {
    if (sortBy !== option) return null
    return (
      <ArrowUpDown className={`w-4 h-4 ml-1 ${sortOrder === 'desc' ? 'rotate-180' : ''}`} />
    )
  }

  return (
    <div className={className}>
      {/* 搜索和工具栏 */}
      <Card className="p-4 mb-4">
        <div className="flex items-center gap-4 mb-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="搜索院校、地区或专业..."
              value={searchKeyword}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            className="flex items-center gap-2"
          >
            {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid3X3 className="w-4 h-4" />}
            {viewMode === 'grid' ? '列表' : '网格'}
          </Button>
        </div>

        {/* 排序选项 */}
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-600">排序:</span>
          <Button
            variant={sortBy === 'ranking' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSortChange('ranking')}
            className="flex items-center"
          >
            <TrendingUp className="w-4 h-4 mr-1" />
            排名
            {getSortIcon('ranking')}
          </Button>
          <Button
            variant={sortBy === 'score' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSortChange('score')}
            className="flex items-center"
          >
            分数
            {getSortIcon('score')}
          </Button>
          <Button
            variant={sortBy === 'name' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSortChange('name')}
            className="flex items-center"
          >
            名称
            {getSortIcon('name')}
          </Button>
          <Button
            variant={sortBy === 'location' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSortChange('location')}
            className="flex items-center"
          >
            <MapPin className="w-4 h-4 mr-1" />
            地区
            {getSortIcon('location')}
          </Button>
          {userScore && userRanking && (
            <Button
              variant={sortBy === 'probability' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('probability')}
              className="flex items-center"
            >
              录取概率
              {getSortIcon('probability')}
            </Button>
          )}
        </div>
      </Card>

      {/* 结果统计 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            找到 <span className="font-semibold text-gray-900">{filteredAndSortedUniversities.length}</span> 所院校
          </span>
          {selectedUniversities.length > 0 && (
            <Badge className="bg-blue-100 text-blue-800">
              已选择 {selectedUniversities.length} 所
            </Badge>
          )}
        </div>
      </div>

      {/* 院校列表 */}
      {filteredAndSortedUniversities.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Filter className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到匹配的院校</h3>
          <p className="text-gray-500 mb-4">请尝试调整筛选条件或搜索关键词</p>
          <Button
            variant="outline"
            onClick={() => {
              setSearchKeyword('')
              onFiltersChange({
                provinces: [],
                types: [],
                categories: [],
                levels: ['本科'],
                scoreRange: { min: 300, max: 750 },
                rankingRange: { min: 1, max: 50000 },
                features: [],
                isPublicOnly: false,
                searchKeyword: ''
              })
            }}
          >
            重置筛选条件
          </Button>
        </Card>
      ) : (
        <div className={`${
          viewMode === 'grid' 
            ? 'grid grid-cols-1 lg:grid-cols-2 gap-4' 
            : 'space-y-4'
        }`}>
          {filteredAndSortedUniversities.map(university => (
            <UniversityCard
              key={university.id}
              university={university}
              userScore={userScore}
              userRanking={userRanking}
              isSelected={selectedUniversities.includes(university.id)}
              onSelect={onUniversitySelect}
              onViewDetail={onUniversityDetail}
              className={viewMode === 'list' ? 'w-full' : ''}
            />
          ))}
        </div>
      )}
    </div>
  )
}

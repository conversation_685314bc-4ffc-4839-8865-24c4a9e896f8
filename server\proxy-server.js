// 百度AppBuilder API代理服务器
// 用于解决CORS跨域问题

const express = require('express');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');
require('dotenv').config();

const app = express();
const PORT = process.env.PROXY_PORT || 3001;

// 启用CORS
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175'],
  credentials: true
}));

// 解析JSON请求体
app.use(express.json());

// 百度AppBuilder API代理
app.use('/api/baidu', createProxyMiddleware({
  target: 'https://qianfan.baidubce.com',
  changeOrigin: true,
  pathRewrite: {
    '^/api/baidu': '', // 移除 /api/baidu 前缀
  },
  onProxyReq: (proxyReq, req, res) => {
    // 添加必要的请求头
    proxyReq.setHeader('Origin', 'https://qianfan.baidubce.com');
    proxyReq.setHeader('Referer', 'https://qianfan.baidubce.com');
    
    // 确保Content-Type正确设置
    if (req.body && Object.keys(req.body).length > 0) {
      const bodyData = JSON.stringify(req.body);
      proxyReq.setHeader('Content-Type', 'application/json');
      proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
      proxyReq.write(bodyData);
    }
  },
  onProxyRes: (proxyRes, req, res) => {
    // 添加CORS头
    proxyRes.headers['Access-Control-Allow-Origin'] = req.headers.origin || '*';
    proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept, Authorization';
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err);
    res.status(500).json({
      error: '代理服务器错误',
      message: err.message
    });
  },
  logLevel: 'debug'
}));

// 咕咕数据API代理
app.use('/api/gugudata', createProxyMiddleware({
  target: 'https://api.gugudata.com',
  changeOrigin: true,
  pathRewrite: {
    '^/api/gugudata': '', // 移除 /api/gugudata 前缀
  },
  onProxyReq: (proxyReq, req, res) => {
    // 添加必要的请求头
    proxyReq.setHeader('Origin', 'https://api.gugudata.com');
    proxyReq.setHeader('Referer', 'https://api.gugudata.com');
    proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    console.log('🔍 代理咕咕数据请求:', req.method, req.url);
    console.log('📋 查询参数:', req.query);
  },
  onProxyRes: (proxyRes, req, res) => {
    // 添加CORS头
    proxyRes.headers['Access-Control-Allow-Origin'] = req.headers.origin || '*';
    proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept, Authorization';

    console.log('✅ 咕咕数据响应状态:', proxyRes.statusCode);
  },
  onError: (err, req, res) => {
    console.error('咕咕数据代理错误:', err);
    res.status(500).json({
      error: '咕咕数据代理服务器错误',
      message: err.message
    });
  },
  logLevel: 'debug'
}));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'Baidu AppBuilder Proxy Server'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    error: '内部服务器错误',
    message: err.message
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.originalUrl
  });
});

app.listen(PORT, () => {
  console.log(`🚀 API代理服务器启动成功！`);
  console.log(`📡 代理服务器地址: http://localhost:${PORT}`);
  console.log(`🔗 百度API代理路径: http://localhost:${PORT}/api/baidu`);
  console.log(`🔗 咕咕数据API代理路径: http://localhost:${PORT}/api/gugudata`);
  console.log(`💚 健康检查: http://localhost:${PORT}/health`);
  console.log(`🌐 支持的前端地址: http://localhost:5173, http://localhost:5174, http://localhost:5175`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});



## 根据条件查询高考一分一段数据


**接口地址**:`/admin-api/system/score-segment/by-condition`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|year|查询的年份，如 2020、2021、2022、2023、2024|query|true|string||
|provincename|省份名称，如：北京、上海、江苏等|query|true|string||
|subjectselection|科目选择类型，如：物理类、历史类等|query|true|string||
|minscore|最小分数|query|false|string||
|maxscore|最大分数|query|false|string||
|batchname|批次名称，如：本科批|query|false|string||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultListScoreSegmentRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||array|ScoreSegmentRespVO|
|&emsp;&emsp;id|主键ID|integer(int64)||
|&emsp;&emsp;year|年份|string||
|&emsp;&emsp;provinceName|省份名称|string||
|&emsp;&emsp;subjectSelection|科目选择类型|string||
|&emsp;&emsp;examinationScore|高考分数|string||
|&emsp;&emsp;candidateCount|该分数考生人数|integer(int32)||
|&emsp;&emsp;totalCandidates|累计考生人数|integer(int32)||
|&emsp;&emsp;rankingRange|位次范围|string||
|&emsp;&emsp;admissionBatchName|录取批次名称|string||
|&emsp;&emsp;minimumAdmissionScore|最低录取控制分数线|string||
|&emsp;&emsp;ranking|位次|string||
|&emsp;&emsp;historicalScores|历史分数数据|string||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": [
		{
			"id": 1,
			"year": "2024",
			"provinceName": "北京",
			"subjectSelection": "物理类",
			"examinationScore": "673",
			"candidateCount": 25,
			"totalCandidates": 100,
			"rankingRange": "1-21",
			"admissionBatchName": "本科批",
			"minimumAdmissionScore": "462",
			"ranking": "673",
			"historicalScores": "[{\"AcademicYear\":2023,\"ExaminationScore\":\"687-750\",\"RankingRange\":\"1-116\"}]"
		}
	],
	"msg": ""
}
```
// 大学API服务
import type { ApiResponse, SearchFilters } from '../types/university'
import { ApiType, getApiKey, getApiPath } from './apiKeys'

// API配置
// 在开发环境中使用代理服务器，生产环境中直接调用API
const isDevelopment = import.meta.env?.MODE === 'development' || typeof window !== 'undefined'
const API_BASE_URL = isDevelopment ? 'http://localhost:3001/api/gugudata' : 'https://api.gugudata.com'

// 调试信息
console.log('🔧 大学API服务配置:')
console.log('- 开发模式:', isDevelopment)
console.log('- API基础URL:', API_BASE_URL)
console.log('- 使用接口: 高校基础信息')
console.log('- API路径:', getApiPath(ApiType.SCHOOL))
console.log('- API密钥:', getApiKey(ApiType.SCHOOL).substring(0, 8) + '...')

/**
 * 构建查询参数
 */
function buildQueryParams(filters: SearchFilters): URLSearchParams {
  const params = new URLSearchParams()

  // 必需参数 - 使用高校基础信息API密钥
  params.append('appkey', getApiKey(ApiType.SCHOOL))
  
  // 可选参数
  if (filters.keywords !== undefined) {
    params.append('keywords', filters.keywords)
  } else {
    params.append('keywords', '') // 空值返回所有数据
  }
  
  if (filters.pagesize !== undefined) {
    params.append('pagesize', Math.min(filters.pagesize, 20).toString()) // 最大20
  } else {
    params.append('pagesize', '10') // 默认10
  }
  
  if (filters.pageindex !== undefined) {
    params.append('pageindex', filters.pageindex.toString())
  } else {
    params.append('pageindex', '1') // 默认第1页
  }
  
  if (filters.keywordstrict !== undefined) {
    params.append('keywordstrict', filters.keywordstrict.toString())
  } else {
    params.append('keywordstrict', 'false') // 默认模糊查询
  }
  
  if (filters.collegecategory) {
    params.append('collegecategory', filters.collegecategory)
  } else {
    params.append('collegecategory', '')
  }
  
  if (filters.collegetype) {
    params.append('collegetype', filters.collegetype)
  } else {
    params.append('collegetype', '')
  }
  
  if (filters.is985 !== undefined) {
    params.append('is985', filters.is985.toString())
  } else {
    params.append('is985', '')
  }
  
  if (filters.is211 !== undefined) {
    params.append('is211', filters.is211.toString())
  } else {
    params.append('is211', '')
  }
  
  if (filters.isdualclass !== undefined) {
    params.append('isdualclass', filters.isdualclass.toString())
  } else {
    params.append('isdualclass', '')
  }
  
  if (filters.edulevel) {
    params.append('edulevel', filters.edulevel)
  } else {
    params.append('edulevel', '')
  }
  
  if (filters.collegeproperty) {
    params.append('collegeproperty', filters.collegeproperty)
  } else {
    params.append('collegeproperty', '')
  }
  
  return params
}

/**
 * 搜索大学
 */
export async function searchUniversities(filters: SearchFilters = {}): Promise<ApiResponse> {
  try {
    const params = buildQueryParams(filters)
    const apiPath = getApiPath(ApiType.SCHOOL)
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`

    console.log('🔍 开始请求大学数据...')
    console.log('📍 请求URL:', url)
    console.log('📋 请求参数:', Object.fromEntries(params))
    console.log('🔑 使用的API类型:', ApiType.SCHOOL)
    console.log('🔑 使用的APPKEY:', getApiKey(ApiType.SCHOOL).substring(0, 8) + '...')

    // 检查是否在浏览器环境中，如果是，可能需要处理CORS问题
    if (typeof window !== 'undefined') {
      console.log('🌐 浏览器环境检测到，可能存在CORS限制')
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      mode: 'cors', // 明确指定CORS模式
    })

    console.log('📡 响应状态:', response.status, response.statusText)
    console.log('📡 响应头:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ HTTP错误响应:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data: ApiResponse = await response.json()
    console.log('✅ API响应数据:', data)

    // 检查API返回的状态码
    if (data.DataStatus?.StatusCode !== 100) {
      console.error('❌ API业务错误:', data.DataStatus)
      throw new Error(`API Error: ${data.DataStatus?.StatusDescription || '未知错误'}`)
    }

    console.log('🎉 成功获取大学数据:', data.Data?.length || 0, '条')
    return data
  } catch (error) {
    console.error('💥 搜索大学失败:', error)

    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求，或考虑使用代理服务器。')
    }

    throw error
  }
}

/**
 * 获取所有大学（分页）
 */
export async function getAllUniversities(page: number = 1, pageSize: number = 10): Promise<ApiResponse> {
  return searchUniversities({
    keywords: '', // 空关键词返回所有数据
    pageindex: page,
    pagesize: pageSize
  })
}

/**
 * 根据关键词搜索大学
 */
export async function searchUniversitiesByKeyword(
  keyword: string, 
  page: number = 1, 
  pageSize: number = 10,
  strict: boolean = false
): Promise<ApiResponse> {
  return searchUniversities({
    keywords: keyword,
    pageindex: page,
    pagesize: pageSize,
    keywordstrict: strict
  })
}

/**
 * 根据类型筛选大学
 */
export async function searchUniversitiesByType(
  filters: {
    is985?: boolean
    is211?: boolean
    isdualclass?: boolean
    collegecategory?: string
    collegetype?: string
    edulevel?: string
    collegeproperty?: string
  },
  page: number = 1,
  pageSize: number = 10
): Promise<ApiResponse> {
  return searchUniversities({
    ...filters,
    pageindex: page,
    pagesize: pageSize
  })
}

// 导出常量供组件使用
export const COLLEGE_CATEGORIES = [
  '理工类', '综合类', '师范类', '财经类', '医药类', '艺术类', 
  '农林类', '军事类', '政法类', '语言类', '体育类', '民族类', '其它'
]

export const COLLEGE_TYPES = [
  '普通本科', '远程教育学院', '中外合作办学', '独立学院', 
  '高职高专', 'HND项目', '其它', '成人教育', '专科（高职）'
]

export const EDU_LEVELS = [
  '普通本科', '本科', '专科（高职）', '专科', '其它'
]

export const COLLEGE_PROPERTIES = [
  '公办', '民办', '中外合作办学'
]

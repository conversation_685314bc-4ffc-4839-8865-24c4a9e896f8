import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { ArrowLeft, School, Search, MapPin, Star } from 'lucide-react'

// 简化的大学类型
interface SimpleUniversity {
  id: string
  name: string
  type: string
  location: string
  score: number
  ranking: number
}

// 模拟数据
const universities: SimpleUniversity[] = [
  { id: '1', name: '清华大学', type: '985', location: '北京', score: 690, ranking: 1 },
  { id: '2', name: '北京大学', type: '985', location: '北京', score: 685, ranking: 2 },
  { id: '3', name: '复旦大学', type: '985', location: '上海', score: 675, ranking: 3 },
  { id: '4', name: '上海交通大学', type: '985', location: '上海', score: 670, ranking: 4 },
  { id: '5', name: '浙江大学', type: '985', location: '杭州', score: 665, ranking: 5 },
  { id: '6', name: '南京大学', type: '985', location: '南京', score: 660, ranking: 6 },
  { id: '7', name: '中国科学技术大学', type: '985', location: '合肥', score: 655, ranking: 7 },
  { id: '8', name: '华中科技大学', type: '985', location: '武汉', score: 640, ranking: 8 },
  { id: '9', name: '西安交通大学', type: '985', location: '西安', score: 635, ranking: 9 },
  { id: '10', name: '北京师范大学', type: '985', location: '北京', score: 630, ranking: 10 },
]

interface SimpleUniversityPageProps {
  onBack?: () => void
}

export function SimpleUniversityPage({ onBack }: SimpleUniversityPageProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('')

  const filteredUniversities = universities.filter(uni => {
    const matchesSearch = uni.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         uni.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !selectedType || uni.type === selectedType
    return matchesSearch && matchesType
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-2">
                <School className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">查大学</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 搜索和筛选 */}
        <Card className="p-6 mb-6">
          <div className="space-y-4">
            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="搜索大学名称或地区..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 快速筛选 */}
            <div className="flex gap-2">
              <Button
                variant={selectedType === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedType('')}
              >
                全部
              </Button>
              <Button
                variant={selectedType === '985' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedType('985')}
              >
                985高校
              </Button>
            </div>
          </div>
        </Card>

        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{filteredUniversities.length}</div>
            <div className="text-sm text-gray-600">找到院校</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {filteredUniversities.filter(u => u.type === '985').length}
            </div>
            <div className="text-sm text-gray-600">985院校</div>
          </Card>
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Math.round(filteredUniversities.reduce((sum, u) => sum + u.score, 0) / filteredUniversities.length) || 0}
            </div>
            <div className="text-sm text-gray-600">平均分数</div>
          </Card>
        </div>

        {/* 大学列表 */}
        <div className="space-y-4">
          {filteredUniversities.length > 0 ? (
            filteredUniversities.map((university) => (
              <Card key={university.id} className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-bold text-gray-900">{university.name}</h3>
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                        {university.type}
                      </span>
                      <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                        <Star className="w-3 h-3" />
                        全国第{university.ranking}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                      <MapPin className="w-4 h-4" />
                      <span>{university.location}</span>
                    </div>
                    
                    <div className="flex gap-4">
                      <Button variant="outline" size="sm">
                        查看详情
                      </Button>
                      <Button variant="ghost" size="sm">
                        官网
                      </Button>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">{university.score}</div>
                    <div className="text-xs text-gray-500">平均分</div>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <Card className="p-12 text-center">
              <School className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的院校</h3>
              <p className="text-gray-600 mb-4">请尝试调整搜索关键词或筛选条件</p>
              <Button onClick={() => { setSearchTerm(''); setSelectedType('') }}>
                清除搜索条件
              </Button>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

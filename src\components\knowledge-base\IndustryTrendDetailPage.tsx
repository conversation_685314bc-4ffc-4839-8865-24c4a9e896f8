import { useState, useEffect } from "react"
import { ArrowLeft, TrendingUp, MapPin, Calendar, Eye, Loader2, AlertCircle, ChevronDown } from "lucide-react"
import { Button } from "../ui/button"
import { 
  getIndustryTrendDetail, 
  formatTimestamp, 
  CHINA_PROVINCES,
  type IndustryTrendItem,
  type IndustryTrendDetailItem 
} from "../../services/industryTrendsApi"

interface IndustryTrendDetailPageProps {
  onBack: () => void
  selectedTrend: IndustryTrendItem | null
}

export function IndustryTrendDetailPage({ onBack, selectedTrend }: IndustryTrendDetailPageProps) {
  const [detailData, setDetailData] = useState<IndustryTrendDetailItem | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedProvince, setSelectedProvince] = useState<string>('')
  const [showProvinceDropdown, setShowProvinceDropdown] = useState(false)

  // 加载详情数据
  const loadDetailData = async (questionId: number, province: string = '') => {
    setLoading(true)
    setError(null)

    try {
      const response = await getIndustryTrendDetail(questionId, '', province)
      if (response.data.list.length > 0) {
        setDetailData(response.data.list[0])
      } else {
        setError('未找到详细内容')
      }
    } catch (err) {
      console.error('加载详情失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 当选中的趋势或省份改变时重新加载数据
  useEffect(() => {
    if (selectedTrend) {
      loadDetailData(selectedTrend.id, selectedProvince)
    }
  }, [selectedTrend, selectedProvince])

  // 处理省份选择
  const handleProvinceSelect = (province: string) => {
    setSelectedProvince(province)
    setShowProvinceDropdown(false)
  }

  if (!selectedTrend) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">未选择行业趋势</h3>
          <p className="text-gray-600 mb-4">请先选择一个行业趋势查看详情</p>
          <Button onClick={onBack}>返回列表</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回列表</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-800">行业趋势详情</h1>
              </div>
            </div>
            
            {/* 省份选择器 */}
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => setShowProvinceDropdown(!showProvinceDropdown)}
                className="flex items-center space-x-2"
              >
                <MapPin className="w-4 h-4" />
                <span>{selectedProvince || '全国'}</span>
                <ChevronDown className="w-4 h-4" />
              </Button>
              
              {showProvinceDropdown && (
                <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 max-h-64 overflow-y-auto z-50">
                  <div className="p-2">
                    <button
                      onClick={() => handleProvinceSelect('')}
                      className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                        selectedProvince === '' 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      全国
                    </button>
                    {CHINA_PROVINCES.map((province) => (
                      <button
                        key={province}
                        onClick={() => handleProvinceSelect(province)}
                        className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                          selectedProvince === province 
                            ? 'bg-blue-100 text-blue-700' 
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        {province}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 趋势标题卡片 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <span className="px-3 py-1 bg-blue-100 text-blue-700 text-sm font-medium rounded-full">
                  {selectedTrend.type}
                </span>
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <Calendar className="w-4 h-4" />
                  <span>{formatTimestamp(selectedTrend.createTime)}</span>
                </div>
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-3">
                {selectedTrend.question}
              </h1>
              <p className="text-lg text-gray-600">
                {selectedTrend.ds}
              </p>
            </div>
          </div>
        </div>

        {/* 详细内容 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600 mr-3" />
              <span className="text-gray-600">加载详细内容中...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button 
                onClick={() => selectedTrend && loadDetailData(selectedTrend.id, selectedProvince)}
                variant="outline"
              >
                重试
              </Button>
            </div>
          ) : detailData ? (
            <div className="prose prose-lg max-w-none">
              <div
                className="text-gray-800 leading-relaxed [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h2]:text-xl [&>h2]:font-semibold [&>h2]:mb-3 [&>h3]:text-lg [&>h3]:font-medium [&>h3]:mb-2 [&>h4]:text-base [&>h4]:font-medium [&>h4]:mb-2 [&>p]:mb-4 [&>ul]:mb-4 [&>ul]:pl-6 [&>li]:mb-2 [&>li]:list-disc [&>hr]:my-6 [&>hr]:border-gray-300 [&>strong]:font-semibold [&>em]:italic"
                dangerouslySetInnerHTML={{ __html: detailData.content }}
              />
            </div>
          ) : (
            <div className="text-center py-12">
              <Eye className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">暂无详细内容</h3>
              <p className="text-gray-600">该行业趋势的详细内容正在完善中</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

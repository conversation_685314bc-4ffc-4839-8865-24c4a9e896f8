// 简单的API测试脚本
// 运行: node src/test-api.js

const https = require('https');
const querystring = require('querystring');

// 测试API配置
const API_BASE_URL = 'https://api.gugudata.com';
const API_PATH = '/location/college';
const APPKEY = 'YOUR_APPKEY_HERE'; // 请替换为实际的APPKEY

// 构建测试请求参数
const params = {
  appkey: APPKEY,
  keywords: '北京大学',
  pagesize: 5,
  pageindex: 1,
  keywordstrict: false,
  collegecategory: '',
  collegetype: '',
  is985: '',
  is211: '',
  isdualclass: '',
  edulevel: '',
  collegeproperty: ''
};

const queryString = querystring.stringify(params);
const url = `${API_BASE_URL}${API_PATH}?${queryString}`;

console.log('测试API请求...');
console.log('请求URL:', url);

const req = https.get(url, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('\n=== API响应 ===');
      console.log('状态码:', response.DataStatus?.StatusCode);
      console.log('状态描述:', response.DataStatus?.StatusDescription);
      console.log('总数据量:', response.DataStatus?.DataTotalCount);
      console.log('返回数据条数:', response.Data?.length || 0);
      
      if (response.Data && response.Data.length > 0) {
        console.log('\n=== 第一条数据示例 ===');
        const firstUniversity = response.Data[0];
        console.log('学校名称:', firstUniversity.CollegeName);
        console.log('所在地区:', firstUniversity.Province, firstUniversity.City);
        console.log('是否985:', firstUniversity.Is985);
        console.log('是否211:', firstUniversity.Is211);
        console.log('是否双一流:', firstUniversity.IsDualClass);
        console.log('院校类别:', firstUniversity.CollegeCategory);
        console.log('院校性质:', firstUniversity.CollegeType);
        console.log('全国排名:', firstUniversity.Ranking);
      }
      
      console.log('\n测试完成！');
    } catch (error) {
      console.error('解析响应失败:', error.message);
      console.log('原始响应:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('请求失败:', error.message);
});

req.setTimeout(10000, () => {
  console.error('请求超时');
  req.destroy();
});

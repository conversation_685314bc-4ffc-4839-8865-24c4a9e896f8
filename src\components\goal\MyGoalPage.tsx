import { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { goalService } from '../../services/goalService'
import { getCollegeScoreLine } from '../../services/scoreLineApiSimple'
import type { UserGoal, ExamScore, GoalAnalysis } from '../../types/goal'
import type { CollegeScoreLineData } from '../../services/scoreLineApiSimple'
import {
  Target,
  Plus,
  TrendingUp,
  TrendingDown,
  Minus,
  Award,
  BookOpen,
  Calendar,
  BarChart3,
  ChevronRight,
  Trophy,
  AlertCircle,
  CheckCircle,
  Edit,
  Trash2,
  School,
  ArrowLeft,
  Home,
  MapPin,
  Clock,
  Users,
  Star,
  Loader2
} from 'lucide-react'

interface MyGoalPageProps {
  onNavigate?: (page: string) => void
  onBack?: () => void
}

export function MyGoalPage({ onNavigate, onBack }: MyGoalPageProps) {
  const { user, isAuthenticated } = useAuth()
  const [goals, setGoals] = useState<UserGoal[]>([])
  const [scores, setScores] = useState<ExamScore[]>([])
  const [analyses, setAnalyses] = useState<Map<string, GoalAnalysis>>(new Map())
  const [activeTab, setActiveTab] = useState<'overview' | 'goals' | 'scores'>('overview')
  const [scoreLineData, setScoreLineData] = useState<Map<string, CollegeScoreLineData[]>>(new Map())
  const [scoreLineLoading, setScoreLineLoading] = useState(false)
  const [loading, setLoading] = useState(true)

  // 加载数据
  useEffect(() => {
    if (isAuthenticated && user) {
      loadData()
    }
  }, [isAuthenticated, user])

  // 获取录取分数线数据
  const fetchScoreLineData = async (goals: UserGoal[]) => {
    if (!user || goals.length === 0) return

    try {
      setScoreLineLoading(true)
      const newScoreLineData = new Map<string, CollegeScoreLineData[]>()

      for (const goal of goals) {
        try {
          const response = await getCollegeScoreLine({
            searchtype: 'COLLEGENAME',
            keyword: goal.universityName,
            pageindex: 1,
            pagesize: 3,
            year: 2024,
            enrollprovince: user.province || '安徽',
            type: '物理类'
          })

          if (response.DataStatus?.StatusCode === 100) {
            const goalId = goal.type === 'university' ? goal.universityId : `${goal.universityId}_${goal.majorId}`
            newScoreLineData.set(goalId, response.Data || [])
          }
        } catch (error) {
          console.error(`获取${goal.universityName}录取分数线失败:`, error)
        }

        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 300))
      }

      setScoreLineData(newScoreLineData)
    } catch (error) {
      console.error('获取录取分数线数据失败:', error)
    } finally {
      setScoreLineLoading(false)
    }
  }

  const loadData = async () => {
    if (!user) return

    try {
      setLoading(true)

      // 加载目标
      const goalsResponse = goalService.getUserGoals(user.id)
      setGoals(goalsResponse.goals)

      // 加载分数
      const scoresResponse = goalService.getExamScores(user.id)
      setScores(scoresResponse.scores)

      // 分析每个目标的进度
      const newAnalyses = new Map<string, GoalAnalysis>()
      for (const goal of goalsResponse.goals) {
        const goalId = goal.type === 'university' ? goal.universityId : `${goal.universityId}_${goal.majorId}`
        const analysis = goalService.analyzeGoalProgress(user.id, goalId)
        if (analysis) {
          newAnalyses.set(goalId, analysis)
        }
      }
      setAnalyses(newAnalyses)

      // 获取录取分数线数据
      if (goalsResponse.goals.length > 0) {
        fetchScoreLineData(goalsResponse.goals)
      }

    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteGoal = async (goal: UserGoal) => {
    if (!user) return

    try {
      const goalId = goal.type === 'university' ? goal.universityId : `${goal.universityId}_${goal.majorId}`
      await goalService.deleteUserGoal(user.id, goalId)
      await loadData()
    } catch (error) {
      console.error('删除目标失败:', error)
    }
  }

  const handleDeleteScore = async (scoreId: string) => {
    if (!user) return

    try {
      await goalService.deleteExamScore(user.id, scoreId)
      await loadData()
    } catch (error) {
      console.error('删除分数失败:', error)
    }
  }

  const getTrendIcon = (trend: 'improving' | 'stable' | 'declining') => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'declining':
        return <TrendingDown className="w-4 h-4 text-red-500" />
      default:
        return <Minus className="w-4 h-4 text-gray-500" />
    }
  }

  const getProgressColor = (progress: number, isAchieved: boolean) => {
    if (isAchieved) return 'bg-green-500'
    if (progress >= 80) return 'bg-blue-500'
    if (progress >= 60) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  // 计算录取可能性
  const calculateAdmissionPossibility = (goal: UserGoal, analysis: GoalAnalysis | undefined, scoreLines: CollegeScoreLineData[]) => {
    if (!analysis || scoreLines.length === 0) {
      return { label: '暂无数据', color: 'text-gray-500', level: 'unknown' }
    }

    const latestScoreLine = scoreLines[0]
    const minScore = latestScoreLine.LowestScore === '-' ? 0 : parseInt(latestScoreLine.LowestScore) || 0
    const avgScore = latestScoreLine.AverageScore === '-' ? 0 : parseInt(latestScoreLine.AverageScore) || 0
    const userScore = analysis.currentScore

    if (avgScore > 0 && userScore >= avgScore + 20) {
      return { label: '录取希望大', color: 'text-green-600', level: 'high' }
    } else if (minScore > 0 && userScore >= minScore) {
      return { label: '有录取希望', color: 'text-yellow-600', level: 'medium' }
    } else if (minScore > 0 && userScore >= minScore - 20) {
      return { label: '录取希望较小', color: 'text-orange-600', level: 'low' }
    } else {
      return { label: '录取困难', color: 'text-red-600', level: 'very-low' }
    }
  }

  // 计算距离高考的时间
  const calculateTimeToGaokao = () => {
    const now = new Date()
    const currentYear = now.getFullYear()
    let gaokaoDate = new Date(currentYear, 5, 7) // 6月7日

    // 如果今年的高考已过，计算到明年高考的时间
    if (now > gaokaoDate) {
      gaokaoDate = new Date(currentYear + 1, 5, 7)
    }

    const diffTime = gaokaoDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > 365) {
      return `${Math.floor(diffDays / 365)}年${Math.floor((diffDays % 365) / 30)}个月`
    } else if (diffDays > 30) {
      return `${Math.floor(diffDays / 30)}个月${diffDays % 30}天`
    } else {
      return `${diffDays}天`
    }
  }

  // 获取最近的考试趋势
  const getRecentTrend = (scores: ExamScore[]) => {
    if (scores.length < 2) return null

    const recent = scores.slice(0, 3)
    const avgRecent = recent.reduce((sum, score) => sum + score.totalScore, 0) / recent.length
    const older = scores.slice(3, 6)

    if (older.length === 0) return null

    const avgOlder = older.reduce((sum, score) => sum + score.totalScore, 0) / older.length
    const improvement = avgRecent - avgOlder

    return {
      improvement: Math.round(improvement * 10) / 10,
      trend: improvement > 5 ? 'improving' : improvement < -5 ? 'declining' : 'stable'
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">请先登录</h2>
          <p className="text-gray-500">登录后即可设置和管理您的学习目标</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                title="返回首页"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">我的目标</h1>
                  <p className="text-gray-500">设定目标，追踪进度，实现梦想</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onBack}
                className="flex items-center space-x-2 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Home className="w-4 h-4" />
                <span>返回首页</span>
              </button>
              <button
                onClick={() => onNavigate?.('goal-setting')}
                className="flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>设置目标</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { key: 'overview', label: '总览', icon: BarChart3 },
              { key: 'goals', label: '我的目标', icon: Target },
              { key: 'scores', label: '成绩记录', icon: Trophy }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                  activeTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* 统计卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">设定目标</p>
                    <p className="text-2xl font-bold text-gray-900">{goals.length}</p>
                  </div>
                  <Target className="w-8 h-8 text-orange-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">已达成</p>
                    <p className="text-2xl font-bold text-green-600">
                      {Array.from(analyses.values()).filter(a => a.isAchieved).length}
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">考试记录</p>
                    <p className="text-2xl font-bold text-blue-600">{scores.length}</p>
                  </div>
                  <Award className="w-8 h-8 text-blue-500" />
                </div>
              </div>
            </div>

            {/* 目标进度概览 */}
            {goals.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">目标进度</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Clock className="w-4 h-4" />
                    <span>距离高考还有 {calculateTimeToGaokao()}</span>
                  </div>
                </div>

                <div className="space-y-6">
                  {goals.map(goal => {
                    const goalId = goal.type === 'university' ? goal.universityId : `${goal.universityId}_${goal.majorId}`
                    const analysis = analyses.get(goalId)
                    const scoreLines = scoreLineData.get(goalId) || []
                    const admissionPossibility = calculateAdmissionPossibility(goal, analysis, scoreLines)
                    const recentTrend = getRecentTrend(scores)

                    return (
                      <div key={goalId} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                        {/* 院校基本信息 */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                              {goal.universityName.charAt(0)}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <h4 className="font-semibold text-gray-900 text-lg">
                                  {goal.universityName}
                                </h4>
                                {goal.is985 && (
                                  <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded font-medium">985</span>
                                )}
                                {goal.is211 && (
                                  <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded font-medium">211</span>
                                )}
                                {goal.isDualClass && (
                                  <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded font-medium">双一流</span>
                                )}
                              </div>

                              {goal.type === 'major' && (
                                <p className="text-purple-700 font-medium mb-1">{goal.majorName}</p>
                              )}

                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <div className="flex items-center space-x-1">
                                  <MapPin className="w-4 h-4" />
                                  <span>{goal.province}</span>
                                </div>
                                {goal.category && (
                                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                    {goal.category}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* 录取可能性标签 */}
                          <div className="text-right">
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${admissionPossibility.color} bg-opacity-10`}>
                              {admissionPossibility.label}
                            </span>
                            {scoreLineLoading && (
                              <div className="flex items-center justify-end mt-1">
                                <Loader2 className="w-3 h-3 animate-spin text-gray-400" />
                                <span className="text-xs text-gray-400 ml-1">加载中...</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* 分数和进度信息 */}
                        {analysis && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                            {/* 左侧：分数进度 */}
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-500">当前分数</span>
                                <div className="flex items-center space-x-2">
                                  {getTrendIcon(analysis.trend)}
                                  <span className={`text-lg font-bold ${
                                    analysis.isAchieved ? 'text-green-600' : 'text-gray-900'
                                  }`}>
                                    {analysis.currentScore}分
                                  </span>
                                </div>
                              </div>

                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-500">目标分数</span>
                                <span className="text-lg font-bold text-blue-600">{analysis.targetScore}分</span>
                              </div>

                              <div className="space-y-1">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-500">完成进度</span>
                                  <span className="font-medium">{analysis.progress}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-3">
                                  <div
                                    className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(analysis.progress, analysis.isAchieved)}`}
                                    style={{ width: `${Math.min(analysis.progress, 100)}%` }}
                                  ></div>
                                </div>
                              </div>

                              {analysis.isAchieved ? (
                                <div className="flex items-center space-x-2 text-green-600">
                                  <CheckCircle className="w-4 h-4" />
                                  <span className="text-sm font-medium">🎉 已达成目标！</span>
                                </div>
                              ) : (
                                <div className="text-sm text-gray-600">
                                  <span>还需提高 </span>
                                  <span className="font-bold text-orange-600">{analysis.scoreDifference}</span>
                                  <span> 分</span>
                                </div>
                              )}
                            </div>

                            {/* 右侧：录取分数线对比 */}
                            <div className="space-y-3">
                              {scoreLines.length > 0 ? (
                                <>
                                  <h5 className="text-sm font-medium text-gray-700 mb-2">2024年录取分数线</h5>
                                  <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                      <span className="text-gray-500">最低分：</span>
                                      <span className="font-medium">
                                        {scoreLines[0].LowestScore === '-' ? '暂无' : `${scoreLines[0].LowestScore}分`}
                                      </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                      <span className="text-gray-500">平均分：</span>
                                      <span className="font-medium">
                                        {scoreLines[0].AverageScore === '-' ? '暂无' : `${scoreLines[0].AverageScore}分`}
                                      </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                      <span className="text-gray-500">录取位次：</span>
                                      <span className="font-medium">
                                        {scoreLines[0].LowestRank || '暂无'}
                                      </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                      <span className="text-gray-500">录取批次：</span>
                                      <span className="font-medium text-blue-600">
                                        {scoreLines[0].AdmissionBatch}
                                      </span>
                                    </div>
                                  </div>
                                </>
                              ) : (
                                <div className="text-center py-4 text-gray-500">
                                  <AlertCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                                  <p className="text-sm">暂无录取分数线数据</p>
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* 趋势和建议 */}
                        {analysis && (
                          <div className="border-t pt-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {/* 最近趋势 */}
                              {recentTrend && (
                                <div className="flex items-center space-x-2 text-sm">
                                  <Users className="w-4 h-4 text-gray-400" />
                                  <span className="text-gray-500">最近趋势：</span>
                                  <span className={`font-medium ${
                                    recentTrend.trend === 'improving' ? 'text-green-600' :
                                    recentTrend.trend === 'declining' ? 'text-red-600' : 'text-gray-600'
                                  }`}>
                                    {recentTrend.improvement > 0 ? '+' : ''}{recentTrend.improvement}分
                                  </span>
                                </div>
                              )}

                              {/* 下一个里程碑 */}
                              {analysis.nextMilestone && (
                                <div className="flex items-center space-x-2 text-sm">
                                  <Star className="w-4 h-4 text-yellow-500" />
                                  <span className="text-gray-500">下一目标：</span>
                                  <span className="font-medium text-yellow-600">
                                    {analysis.nextMilestone.score}分
                                  </span>
                                </div>
                              )}
                            </div>

                            {/* 建议 */}
                            {analysis.recommendation && (
                              <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                                <p className="text-sm text-blue-800">{analysis.recommendation}</p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* 空状态 */}
            {goals.length === 0 && (
              <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                <Target className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有设定目标</h3>
                <p className="text-gray-500 mb-6">设定一个学习目标，开始您的追梦之旅</p>
                <button
                  onClick={() => onNavigate?.('goal-setting')}
                  className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                >
                  设置第一个目标
                </button>
              </div>
            )}
          </div>
        )}

        {/* 目标管理标签页 */}
        {activeTab === 'goals' && (
          <div className="space-y-6">
            {goals.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {goals.map(goal => {
                  const goalId = goal.type === 'university' ? goal.universityId : `${goal.universityId}_${goal.majorId}`
                  const analysis = analyses.get(goalId)

                  return (
                    <div key={goalId} className="bg-white rounded-lg shadow-sm p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            {goal.type === 'university' ? (
                              <School className="w-5 h-5 text-white" />
                            ) : (
                              <BookOpen className="w-5 h-5 text-white" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {goal.universityName}
                            </h3>
                            {goal.type === 'major' && (
                              <p className="text-orange-600 font-medium text-sm">
                                {goal.majorName}
                              </p>
                            )}
                            <p className="text-sm text-gray-500">{goal.province}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              {goal.is985 && (
                                <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded">985</span>
                              )}
                              {goal.is211 && (
                                <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">211</span>
                              )}
                              {goal.isDualClass && (
                                <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">双一流</span>
                              )}
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => handleDeleteGoal(goal)}
                          className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>

                      {analysis && (
                        <div className="space-y-3">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">目标分数</span>
                            <span className="font-medium">{analysis.targetScore}分</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">当前分数</span>
                            <div className="flex items-center space-x-2">
                              {getTrendIcon(analysis.trend)}
                              <span className="font-medium">{analysis.currentScore}分</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">进度</span>
                            <span className="font-medium">{analysis.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(analysis.progress, analysis.isAchieved)}`}
                              style={{ width: `${Math.min(analysis.progress, 100)}%` }}
                            ></div>
                          </div>
                          {analysis.isAchieved ? (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-500" />
                                <span className="text-sm text-green-700 font-medium">🎉 已达成目标！</span>
                              </div>
                            </div>
                          ) : (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                              <p className="text-sm text-blue-700">
                                还需提高 <span className="font-medium">{analysis.scoreDifference}</span> 分
                              </p>
                              {analysis.nextMilestone && (
                                <p className="text-xs text-blue-600 mt-1">
                                  {analysis.nextMilestone.description}
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      {!analysis && goal.targetScore && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <p className="text-sm text-yellow-700">
                            目标分数: {goal.targetScore}分 - 请录入考试分数以查看分析
                          </p>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                <Target className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有设定目标</h3>
                <p className="text-gray-500 mb-6">设定一个学习目标，开始您的追梦之旅</p>
                <button
                  onClick={() => onNavigate?.('goal-setting')}
                  className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                >
                  设置第一个目标
                </button>
              </div>
            )}
          </div>
        )}

        {/* 成绩记录标签页 */}
        {activeTab === 'scores' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">考试记录</h3>
              <button
                onClick={() => onNavigate?.('score-entry')}
                className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>录入分数</span>
              </button>
            </div>

            {scores.length > 0 ? (
              <div className="space-y-4">
                {scores.map(score => (
                  <div key={score.id} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h4 className="font-medium text-gray-900">{score.examName}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(score.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Award className="w-4 h-4" />
                            <span className="font-medium text-lg text-gray-900">{score.totalScore}分</span>
                          </div>
                          {score.ranking && (
                            <span>年级排名: {score.ranking}</span>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() => handleDeleteScore(score.id)}
                        className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>

                    {/* 各科分数 */}
                    <div className="grid grid-cols-3 md:grid-cols-6 gap-3 mb-4">
                      {Object.entries(score.subjects).map(([subject, subjectScore]) => (
                        <div key={subject} className="text-center">
                          <div className="text-xs text-gray-500">{subject}</div>
                          <div className="font-medium text-gray-900">{subjectScore}</div>
                        </div>
                      ))}
                    </div>

                    {score.notes && (
                      <div className="bg-gray-50 rounded-lg p-3">
                        <p className="text-sm text-gray-600">{score.notes}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有考试记录</h3>
                <p className="text-gray-500 mb-6">录入您的考试分数，追踪学习进度</p>
                <button
                  onClick={() => onNavigate?.('score-entry')}
                  className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                >
                  录入第一次考试
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

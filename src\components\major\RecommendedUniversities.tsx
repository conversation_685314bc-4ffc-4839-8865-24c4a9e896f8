import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import { mockUniversities } from '../../data/universities'
import { 
  Star,
  MapPin,
  TrendingUp,
  Users,
  ExternalLink,
  Award
} from 'lucide-react'

interface RecommendedUniversitiesProps {
  className?: string
  maxCount?: number
}

export function RecommendedUniversities({ className, maxCount = 10 }: RecommendedUniversitiesProps) {
  // 获取推荐院校（按排名排序，取前N个）
  const recommendedUniversities = mockUniversities
    .filter(uni => uni.ranking?.national)
    .sort((a, b) => (a.ranking?.national || 999) - (b.ranking?.national || 999))
    .slice(0, maxCount)

  const getTypeColor = (type: string) => {
    switch (type) {
      case '985':
        return 'bg-red-100 text-red-800 border-red-200'
      case '211':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case '双一流':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatScore = (score?: number) => {
    return score ? `${score}分` : 'N/A'
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 推荐院校标题 */}
      <Card className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Award className="w-5 h-5 text-yellow-600" />
          <h3 className="text-lg font-bold text-gray-900">推荐院校</h3>
        </div>
        
        <div className="space-y-3">
          {recommendedUniversities.map((university, index) => (
            <div
              key={university.id}
              className="group p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors cursor-pointer border border-gray-200 hover:border-gray-300"
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-bold text-blue-600">
                      #{index + 1}
                    </span>
                    <h4 className="font-medium text-gray-900 text-sm group-hover:text-blue-600 transition-colors">
                      {university.name}
                    </h4>
                  </div>
                  
                  <div className="flex items-center gap-2 mb-2">
                    <span className={cn(
                      "px-2 py-0.5 rounded text-xs font-medium border",
                      getTypeColor(university.type)
                    )}>
                      {university.type}
                    </span>
                    
                    {university.ranking?.national && (
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 text-yellow-500" />
                        <span className="text-xs text-gray-600">
                          全国第{university.ranking.national}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3 text-xs text-gray-600">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span>{university.location.city}</span>
                    </div>
                    
                    {university.admissionInfo?.avgScore && (
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-3 h-3" />
                        <span>{formatScore(university.admissionInfo.avgScore)}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
              </div>
              
              {/* 院校特色 */}
              {university.features && university.features.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {university.features.slice(0, 2).map((feature, featureIndex) => (
                    <span 
                      key={featureIndex}
                      className="px-1.5 py-0.5 bg-green-50 text-green-700 rounded text-xs"
                    >
                      {feature}
                    </span>
                  ))}
                  {university.features.length > 2 && (
                    <span className="px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded text-xs">
                      +{university.features.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
        
        <Button variant="outline" className="w-full mt-4" size="sm">
          查看更多院校
        </Button>
      </Card>

      {/* 志愿填报提醒 */}
      <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
            <Users className="w-4 h-4 text-white" />
          </div>
          <div>
            <h4 className="font-bold text-blue-900 mb-2">志愿填报提醒</h4>
            <p className="text-sm text-blue-800 mb-3">
              选择专业时要综合考虑个人兴趣、就业前景、院校实力等因素。
            </p>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              获取填报建议
            </Button>
          </div>
        </div>
      </Card>

      {/* 热门专业统计 */}
      <Card className="p-4">
        <h4 className="font-bold text-gray-900 mb-3">热门专业TOP5</h4>
        <div className="space-y-2">
          {[
            { name: '计算机科学与技术', heat: 95 },
            { name: '临床医学', heat: 88 },
            { name: '人工智能', heat: 85 },
            { name: '电气工程及其自动化', heat: 82 },
            { name: '法学', heat: 78 }
          ].map((item, index) => (
            <div key={index} className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 w-4">
                {index + 1}
              </span>
              <div className="flex-1">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm text-gray-900">{item.name}</span>
                  <span className="text-xs text-gray-600">{item.heat}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${item.heat}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
}

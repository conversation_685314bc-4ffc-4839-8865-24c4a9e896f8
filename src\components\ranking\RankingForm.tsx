import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { provinces, availableYears, categories } from '../../data/rankings'

// 本地类型定义
interface RankingQuery {
  year: number
  province: string
  score: number
  category: '文科' | '理科' | '综合'
}
import { cn } from '../../lib/utils'
import { 
  Search, 
  Calendar, 
  MapPin, 
  GraduationCap, 
  Target,
  ChevronDown,
  Loader2
} from 'lucide-react'

interface RankingFormProps {
  query: RankingQuery
  onChange: (query: RankingQuery) => void
  onSearch: () => void
  isLoading?: boolean
}

export function RankingForm({ query, onChange, onSearch, isLoading }: RankingFormProps) {
  const [showYearDropdown, setShowYearDropdown] = useState(false)
  const [showProvinceDropdown, setShowProvinceDropdown] = useState(false)
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false)

  const handleScoreChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const score = value === '' ? 0 : parseInt(value)
    if (!isNaN(score) && score >= 0 && score <= 750) {
      onChange({ ...query, score })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (query.score > 0) {
      onSearch()
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 年份选择 */}
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <Calendar className="w-4 h-4" />
          年份
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowYearDropdown(!showYearDropdown)}
            className="w-full px-4 py-3 text-left bg-white border border-gray-200 rounded-lg hover:border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all"
          >
            <div className="flex items-center justify-between">
              <span className="text-gray-900">{query.year}年</span>
              <ChevronDown className={cn(
                "w-4 h-4 text-gray-500 transition-transform",
                showYearDropdown && "rotate-180"
              )} />
            </div>
          </button>
          
          {showYearDropdown && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto">
              {availableYears.map(year => (
                <button
                  key={year}
                  type="button"
                  onClick={() => {
                    onChange({ ...query, year })
                    setShowYearDropdown(false)
                  }}
                  className={cn(
                    "w-full px-4 py-2 text-left hover:bg-orange-50 transition-colors",
                    query.year === year && "bg-orange-100 text-orange-900"
                  )}
                >
                  {year}年
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 省份选择 */}
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <MapPin className="w-4 h-4" />
          省份
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowProvinceDropdown(!showProvinceDropdown)}
            className="w-full px-4 py-3 text-left bg-white border border-gray-200 rounded-lg hover:border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all"
          >
            <div className="flex items-center justify-between">
              <span className="text-gray-900">{query.province}</span>
              <ChevronDown className={cn(
                "w-4 h-4 text-gray-500 transition-transform",
                showProvinceDropdown && "rotate-180"
              )} />
            </div>
          </button>
          
          {showProvinceDropdown && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto">
              {provinces.map(province => (
                <button
                  key={province}
                  type="button"
                  onClick={() => {
                    onChange({ ...query, province })
                    setShowProvinceDropdown(false)
                  }}
                  className={cn(
                    "w-full px-4 py-2 text-left hover:bg-orange-50 transition-colors",
                    query.province === province && "bg-orange-100 text-orange-900"
                  )}
                >
                  {province}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 科类选择 */}
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <GraduationCap className="w-4 h-4" />
          科类
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className="w-full px-4 py-3 text-left bg-white border border-gray-200 rounded-lg hover:border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all"
          >
            <div className="flex items-center justify-between">
              <span className="text-gray-900">{query.category}</span>
              <ChevronDown className={cn(
                "w-4 h-4 text-gray-500 transition-transform",
                showCategoryDropdown && "rotate-180"
              )} />
            </div>
          </button>
          
          {showCategoryDropdown && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
              {categories.map(category => (
                <button
                  key={category}
                  type="button"
                  onClick={() => {
                    onChange({ ...query, category })
                    setShowCategoryDropdown(false)
                  }}
                  className={cn(
                    "w-full px-4 py-2 text-left hover:bg-orange-50 transition-colors",
                    query.category === category && "bg-orange-100 text-orange-900"
                  )}
                >
                  {category}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 分数输入 */}
      <div className="space-y-2">
        <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <Target className="w-4 h-4" />
          高考分数
        </label>
        <div className="relative">
          <input
            type="number"
            min="0"
            max="750"
            value={query.score || ''}
            onChange={handleScoreChange}
            placeholder="请输入高考分数"
            className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg hover:border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all text-lg font-medium text-center"
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-500">
            分
          </div>
        </div>
        <div className="text-xs text-gray-500 text-center">
          分数范围：0-750分
        </div>
      </div>

      {/* 查询按钮 */}
      <Button
        type="submit"
        disabled={isLoading || query.score <= 0}
        className="w-full py-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-medium text-lg rounded-lg shadow-lg hover:shadow-xl transition-all"
      >
        {isLoading ? (
          <div className="flex items-center gap-2">
            <Loader2 className="w-5 h-5 animate-spin" />
            查询中...
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            查询位次
          </div>
        )}
      </Button>
    </form>
  )
}

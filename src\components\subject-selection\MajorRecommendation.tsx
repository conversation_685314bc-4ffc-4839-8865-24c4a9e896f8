import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import type { UserSelection, MajorRequirement } from './types'
import { majorRequirements } from './data'
import { 
  BookOpen, 
  TrendingUp, 
  DollarSign,
  Users,
  AlertCircle,
  CheckCircle,
  Star,
  ArrowRight,
  Briefcase,
  GraduationCap
} from 'lucide-react'

interface MajorRecommendationProps {
  selection: UserSelection
  className?: string
}

export function MajorRecommendation({ selection, className }: MajorRecommendationProps) {
  // 根据选科组合推荐专业
  const getRecommendedMajors = (): MajorRequirement[] => {
    if (!selection.isComplete || !selection.firstChoice) return []
    
    const userSubjects = [selection.firstChoice, ...selection.secondChoices]
    
    // 扩展专业数据（实际应用中应该从API获取）
    const allMajors: MajorRequirement[] = [
      ...majorRequirements,
      // 工学类专业
      {
        majorId: 'software-engineering',
        majorName: '软件工程',
        category: '工学',
        requiredSubjects: ['物理'],
        preferredSubjects: ['化学'],
        description: '培养软件系统设计、开发、测试和维护的专业人才',
        employmentRate: 96.8,
        averageSalary: 13500,
        difficulty: 'hard'
      },
      {
        majorId: 'electrical-engineering',
        majorName: '电气工程及其自动化',
        category: '工学',
        requiredSubjects: ['物理'],
        preferredSubjects: ['化学'],
        description: '培养电气工程领域的高级工程技术人才',
        employmentRate: 94.2,
        averageSalary: 11200,
        difficulty: 'hard'
      },
      {
        majorId: 'mechanical-engineering',
        majorName: '机械工程',
        category: '工学',
        requiredSubjects: ['物理'],
        preferredSubjects: ['化学'],
        description: '培养机械设计制造及自动化的工程技术人才',
        employmentRate: 92.5,
        averageSalary: 9800,
        difficulty: 'medium'
      },
      // 理学类专业
      {
        majorId: 'mathematics',
        majorName: '数学与应用数学',
        category: '理学',
        requiredSubjects: ['物理'],
        preferredSubjects: [],
        description: '培养数学理论研究和应用的专业人才',
        employmentRate: 88.3,
        averageSalary: 8900,
        difficulty: 'hard'
      },
      {
        majorId: 'chemistry',
        majorName: '化学',
        category: '理学',
        requiredSubjects: ['物理', '化学'],
        preferredSubjects: [],
        description: '培养化学研究和应用的专业人才',
        employmentRate: 85.7,
        averageSalary: 8200,
        difficulty: 'hard'
      },
      {
        majorId: 'biology',
        majorName: '生物科学',
        category: '理学',
        requiredSubjects: ['物理', '化学', '生物'],
        preferredSubjects: [],
        description: '培养生物科学研究和应用的专业人才',
        employmentRate: 82.4,
        averageSalary: 7800,
        difficulty: 'medium'
      },
      // 医学类专业
      {
        majorId: 'pharmacy',
        majorName: '药学',
        category: '医学',
        requiredSubjects: ['物理', '化学', '生物'],
        preferredSubjects: [],
        description: '培养药物研发、生产和使用的专业人才',
        employmentRate: 91.6,
        averageSalary: 9500,
        difficulty: 'hard'
      },
      // 文学类专业
      {
        majorId: 'chinese-literature',
        majorName: '汉语言文学',
        category: '文学',
        requiredSubjects: [],
        preferredSubjects: ['历史', '政治'],
        description: '培养中文教学、文学创作和文化传播人才',
        employmentRate: 79.2,
        averageSalary: 6800,
        difficulty: 'medium'
      },
      {
        majorId: 'english',
        majorName: '英语',
        category: '文学',
        requiredSubjects: [],
        preferredSubjects: ['历史'],
        description: '培养英语教学、翻译和国际交流人才',
        employmentRate: 81.5,
        averageSalary: 7200,
        difficulty: 'medium'
      },
      // 管理学类专业
      {
        majorId: 'business-administration',
        majorName: '工商管理',
        category: '管理学',
        requiredSubjects: [],
        preferredSubjects: ['历史', '政治'],
        description: '培养企业管理和商业运营的专业人才',
        employmentRate: 86.3,
        averageSalary: 8500,
        difficulty: 'medium'
      },
      // 经济学类专业
      {
        majorId: 'economics',
        majorName: '经济学',
        category: '经济学',
        requiredSubjects: [],
        preferredSubjects: ['历史', '政治'],
        description: '培养经济分析和政策制定的专业人才',
        employmentRate: 84.7,
        averageSalary: 9200,
        difficulty: 'medium'
      }
    ]
    
    // 筛选可报考的专业
    const eligibleMajors = allMajors.filter(major => {
      // 检查必选科目
      const hasRequiredSubjects = major.requiredSubjects.every(subject => 
        userSubjects.includes(subject)
      )
      return hasRequiredSubjects
    })
    
    // 按推荐程度排序
    return eligibleMajors.sort((a, b) => {
      // 优先推荐有偏好科目的专业
      const aPreferredCount = a.preferredSubjects.filter(subject => 
        userSubjects.includes(subject)
      ).length
      const bPreferredCount = b.preferredSubjects.filter(subject => 
        userSubjects.includes(subject)
      ).length
      
      if (aPreferredCount !== bPreferredCount) {
        return bPreferredCount - aPreferredCount
      }
      
      // 其次按就业率排序
      return b.employmentRate - a.employmentRate
    })
  }

  const recommendedMajors = getRecommendedMajors()

  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'hard': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  // 获取就业率颜色
  const getEmploymentColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600'
    if (rate >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (!selection.isComplete) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-gray-400" />
            专业推荐
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">请先完成选科，再查看专业推荐</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 推荐概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-blue-500" />
            专业推荐
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              {recommendedMajors.length}个可选专业
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            基于您的选科组合：{selection.firstChoice} + {selection.secondChoices.join(' + ')}
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">
                {recommendedMajors.length}
              </div>
              <div className="text-sm text-gray-600">可选专业</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <TrendingUp className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">
                {recommendedMajors.length > 0 
                  ? Math.round(recommendedMajors.reduce((sum, major) => sum + major.employmentRate, 0) / recommendedMajors.length)
                  : 0}%
              </div>
              <div className="text-sm text-gray-600">平均就业率</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <DollarSign className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">
                {recommendedMajors.length > 0 
                  ? Math.round(recommendedMajors.reduce((sum, major) => sum + major.averageSalary, 0) / recommendedMajors.length)
                  : 0}
              </div>
              <div className="text-sm text-gray-600">平均薪资(元)</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 专业列表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-orange-500" />
            推荐专业列表
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recommendedMajors.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="w-12 h-12 text-orange-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">暂无匹配的专业推荐</p>
              <p className="text-sm text-gray-500">
                您的选科组合可能比较特殊，建议咨询专业老师
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {recommendedMajors.slice(0, 8).map((major, index) => (
                <div 
                  key={major.majorId}
                  className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="bg-orange-50 text-orange-700">
                          #{index + 1}
                        </Badge>
                        <h3 className="font-semibold text-gray-900">{major.majorName}</h3>
                        <Badge variant="outline" className="bg-gray-50 text-gray-700">
                          {major.category}
                        </Badge>
                        <Badge className={getDifficultyColor(major.difficulty)}>
                          {major.difficulty === 'easy' ? '简单' : 
                           major.difficulty === 'medium' ? '中等' : '困难'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        {major.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <TrendingUp className="w-4 h-4 text-green-500" />
                          <span className={getEmploymentColor(major.employmentRate)}>
                            就业率 {major.employmentRate}%
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4 text-purple-500" />
                          <span className="text-purple-600">
                            平均薪资 {major.averageSalary}元
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {/* 科目要求 */}
                  <div className="flex items-center gap-2 text-xs">
                    {major.requiredSubjects.length > 0 && (
                      <div className="flex items-center gap-1">
                        <span className="text-gray-500">必选：</span>
                        {major.requiredSubjects.map(subject => (
                          <Badge key={subject} variant="outline" className="text-xs bg-red-50 text-red-700">
                            {subject}
                          </Badge>
                        ))}
                      </div>
                    )}
                    {major.preferredSubjects.length > 0 && (
                      <div className="flex items-center gap-1">
                        <span className="text-gray-500">优选：</span>
                        {major.preferredSubjects.map(subject => (
                          <Badge key={subject} variant="outline" className="text-xs bg-blue-50 text-blue-700">
                            {subject}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {recommendedMajors.length > 8 && (
                <div className="text-center pt-4">
                  <Button variant="outline">
                    查看更多专业
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// 志愿100问API服务
export interface VolunteerQAItem {
  id: number
  content: string | null
  type: string
  parentType: string
  question: string
  version: string | null
  createTime: number
  star: string | null
  ds: string
}

export interface VolunteerQADetailItem {
  id: number
  questionId: number
  content: string
  type: string
  parentType: string
  question: string | null
  version: string | null
  createTime: number
  star: string | null
  ds: string
  dsCus: string
}

export interface VolunteerQAListResponse {
  code: number
  data: {
    list: VolunteerQAItem[]
    total: number
  }
  msg: string
}

export interface VolunteerQADetailResponse {
  code: number
  data: {
    list: VolunteerQADetailItem[]
    total: number
  }
  msg: string
}

// API配置
const API_BASE_URL = 'https://m.kefeichangduo.top'

/**
 * 获取志愿100问列表
 */
export async function getVolunteerQAList(
  pageNo: number = 1,
  pageSize: number = 1000
): Promise<VolunteerQAListResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question/page?parentType=2&pageNo=${pageNo}&pageSize=${pageSize}`
    
    console.log('🔍 请求志愿100问列表:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: VolunteerQAListResponse = await response.json()
    console.log('✅ 志愿100问列表响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取志愿100问列表失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 获取志愿100问详情
 */
export async function getVolunteerQADetail(
  questionId: number,
  version: string = '',
  dsCus: string = ''
): Promise<VolunteerQADetailResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question-content/page?questionId=${questionId}&version=${version}&dsCus=${encodeURIComponent(dsCus)}`
    
    console.log('🔍 请求志愿100问详情:', url)
    console.log('📋 请求参数:', { questionId, version, dsCus })
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: VolunteerQADetailResponse = await response.json()
    console.log('✅ 志愿100问详情响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取志愿100问详情失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 格式化时间戳为可读日期
 */
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 根据问题类型获取分类信息
 */
export function getCategoryInfo(type: string): { name: string; color: string; bgColor: string } {
  const categoryMap: Record<string, { name: string; color: string; bgColor: string }> = {
    '政策解读': { name: '政策解读', color: 'text-blue-700', bgColor: 'bg-blue-100' },
    '分数定位': { name: '分数定位', color: 'text-green-700', bgColor: 'bg-green-100' },
    '院校选择': { name: '院校选择', color: 'text-purple-700', bgColor: 'bg-purple-100' },
    '专业选择': { name: '专业选择', color: 'text-orange-700', bgColor: 'bg-orange-100' },
    '填报策略': { name: '填报策略', color: 'text-red-700', bgColor: 'bg-red-100' },
    '特殊类型招生': { name: '特殊类型招生', color: 'text-indigo-700', bgColor: 'bg-indigo-100' },
    '录取规则': { name: '录取规则', color: 'text-pink-700', bgColor: 'bg-pink-100' },
    '后续发展': { name: '后续发展', color: 'text-teal-700', bgColor: 'bg-teal-100' }
  }
  
  return categoryMap[type] || { name: type, color: 'text-gray-700', bgColor: 'bg-gray-100' }
}

/**
 * 获取所有分类统计
 */
export function getCategoryStats(qaItems: VolunteerQAItem[]): Array<{ id: string; name: string; count: number }> {
  const categoryCount: Record<string, number> = {}
  
  qaItems.forEach(item => {
    categoryCount[item.type] = (categoryCount[item.type] || 0) + 1
  })
  
  const categories = [
    { id: 'all', name: '全部问题', count: qaItems.length }
  ]
  
  Object.entries(categoryCount).forEach(([type, count]) => {
    categories.push({
      id: type,
      name: type,
      count
    })
  })
  
  return categories
}

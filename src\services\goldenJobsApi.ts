// 金饭碗API服务
export interface GoldenJobItem {
  id: number
  name: string
  schools: string
  schoolIds: string
  status: number
  sort: number | null
  createTime: number
}

export interface ProvinceItem {
  id: number
  sort: number
  label: string
  value: string
  dictType: string
  status: number
  colorType: string
  cssClass: string
  remark: string
  createTime: number
}

export interface JobDetailItem {
  id: number
  dirName: string
  dirId: number
  name: string
  schoolId: string
  schoolName: string
  content: string
  status: number
  sort: number | null
  createTime: number
}

export interface GoldenJobsListResponse {
  code: number
  data: {
    list: GoldenJobItem[]
    total: number
  }
  msg: string
}

export interface ProvinceListResponse {
  code: number
  data: {
    list: ProvinceItem[]
    total: number
  }
  msg: string
}

export interface JobDetailResponse {
  code: number
  data: {
    list: JobDetailItem[]
    total: number
  }
  msg: string
}

// API配置
const API_BASE_URL = 'https://m.kefeichangduo.top'

/**
 * 获取金饭碗企业列表
 */
export async function getGoldenJobsList(
  pageNo: number = 1,
  pageSize: number = 100
): Promise<GoldenJobsListResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/kf/employment-dir/page?pageNo=${pageNo}&pageSize=${pageSize}`
    
    console.log('🔍 请求金饭碗企业列表:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: GoldenJobsListResponse = await response.json()
    console.log('✅ 金饭碗企业列表响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取金饭碗企业列表失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 获取省份列表
 */
export async function getProvinceList(
  pageNo: number = 1,
  pageSize: number = 100
): Promise<ProvinceListResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/dict-data/page?pageNo=${pageNo}&pageSize=${pageSize}&dictType=province`
    
    console.log('🔍 请求省份列表:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: ProvinceListResponse = await response.json()
    console.log('✅ 省份列表响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取省份列表失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 获取就业详情
 */
export async function getJobDetail(
  dirName: string,
  schoolName: string,
  name: string = '',
  pageNo: number = 1,
  pageSize: number = 100
): Promise<JobDetailResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/kf/employment-essay/page?pageNo=${pageNo}&pageSize=${pageSize}&dirName=${encodeURIComponent(dirName)}&name=${encodeURIComponent(name)}&schoolName=${encodeURIComponent(schoolName)}`
    
    console.log('🔍 请求就业详情:', url)
    console.log('📋 请求参数:', { dirName, schoolName, name, pageNo, pageSize })
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: JobDetailResponse = await response.json()
    console.log('✅ 就业详情响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取就业详情失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 格式化时间戳为可读日期
 */
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 解析学校列表
 */
export function parseSchools(schoolsString: string): string[] {
  return schoolsString.split(',').map(school => school.trim()).filter(school => school.length > 0)
}

/**
 * 获取企业类型信息
 */
export function getCompanyTypeInfo(companyName: string): { type: string; color: string; bgColor: string; icon: string } {
  const typeMap: Record<string, { type: string; color: string; bgColor: string; icon: string }> = {
    '中芯国际': { type: '芯片制造', color: 'text-blue-700', bgColor: 'bg-blue-100', icon: '🔬' },
    '中国中车': { type: '轨道交通', color: 'text-green-700', bgColor: 'bg-green-100', icon: '🚄' },
    '中国华为': { type: '通信科技', color: 'text-red-700', bgColor: 'bg-red-100', icon: '📱' },
    '中核工业': { type: '核能工业', color: 'text-orange-700', bgColor: 'bg-orange-100', icon: '⚛️' },
    '国家能源集团': { type: '能源电力', color: 'text-yellow-700', bgColor: 'bg-yellow-100', icon: '⚡' },
    '中国电子': { type: '电子信息', color: 'text-purple-700', bgColor: 'bg-purple-100', icon: '💻' },
    '国有船舶': { type: '船舶制造', color: 'text-cyan-700', bgColor: 'bg-cyan-100', icon: '🚢' },
    '航天军工': { type: '航天军工', color: 'text-indigo-700', bgColor: 'bg-indigo-100', icon: '🚀' },
    '邮政体系': { type: '邮政物流', color: 'text-teal-700', bgColor: 'bg-teal-100', icon: '📮' },
    '国有银行': { type: '金融银行', color: 'text-emerald-700', bgColor: 'bg-emerald-100', icon: '🏦' },
    '人民警察': { type: '公安司法', color: 'text-slate-700', bgColor: 'bg-slate-100', icon: '👮' },
    '部队军士': { type: '军队体系', color: 'text-stone-700', bgColor: 'bg-stone-100', icon: '🎖️' },
    '免费医学生': { type: '医疗卫生', color: 'text-rose-700', bgColor: 'bg-rose-100', icon: '🏥' },
    '公费师范生': { type: '教育培训', color: 'text-amber-700', bgColor: 'bg-amber-100', icon: '🎓' },
    '中国石油': { type: '石油化工', color: 'text-lime-700', bgColor: 'bg-lime-100', icon: '🛢️' },
    '中国铁路': { type: '铁路运输', color: 'text-violet-700', bgColor: 'bg-violet-100', icon: '🚂' },
    '中国烟草': { type: '烟草专卖', color: 'text-fuchsia-700', bgColor: 'bg-fuchsia-100', icon: '🚬' },
    '国家电网': { type: '电力系统', color: 'text-sky-700', bgColor: 'bg-sky-100', icon: '🔌' }
  }
  
  return typeMap[companyName] || { type: '其他', color: 'text-gray-700', bgColor: 'bg-gray-100', icon: '🏢' }
}

/**
 * 获取企业规模信息
 */
export function getCompanyScale(schoolCount: number): { scale: string; color: string } {
  if (schoolCount >= 50) {
    return { scale: '超大型', color: 'text-red-600' }
  } else if (schoolCount >= 30) {
    return { scale: '大型', color: 'text-orange-600' }
  } else if (schoolCount >= 15) {
    return { scale: '中型', color: 'text-yellow-600' }
  } else {
    return { scale: '小型', color: 'text-green-600' }
  }
}

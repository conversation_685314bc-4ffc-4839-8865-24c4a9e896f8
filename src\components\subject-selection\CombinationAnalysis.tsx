import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { cn } from '../../lib/utils'
import type { SubjectCombination, UserSelection } from './types'
import { popularCombinations } from './data'
import { 
  TrendingUp, 
  Users, 
  Target, 
  Award,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  Star
} from 'lucide-react'

interface CombinationAnalysisProps {
  selection: UserSelection
  className?: string
}

export function CombinationAnalysis({ selection, className }: CombinationAnalysisProps) {
  // 根据用户选择找到对应的组合
  const findMatchingCombination = (): SubjectCombination | null => {
    if (!selection.isComplete || !selection.firstChoice) return null
    
    return popularCombinations.find(combo => 
      combo.firstChoice === selection.firstChoice &&
      combo.secondChoices.length === selection.secondChoices.length &&
      combo.secondChoices.every(subject => selection.secondChoices.includes(subject))
    ) || null
  }

  const currentCombination = findMatchingCombination()

  // 获取难度等级描述
  const getDifficultyDescription = (difficulty: number) => {
    if (difficulty <= 2) return { text: '较容易', color: 'text-green-600 bg-green-50' }
    if (difficulty <= 3) return { text: '中等', color: 'text-yellow-600 bg-yellow-50' }
    if (difficulty <= 4) return { text: '较困难', color: 'text-orange-600 bg-orange-50' }
    return { text: '很困难', color: 'text-red-600 bg-red-50' }
  }

  // 获取热门程度描述
  const getPopularityDescription = (rank: number) => {
    if (rank <= 2) return { text: '非常热门', color: 'text-red-600 bg-red-50' }
    if (rank <= 4) return { text: '比较热门', color: 'text-orange-600 bg-orange-50' }
    return { text: '相对冷门', color: 'text-blue-600 bg-blue-50' }
  }

  if (!selection.isComplete) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-gray-400" />
            组合分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">请先完成选科，再查看组合分析</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!currentCombination) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-orange-500" />
            组合分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-orange-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">这是一个较少见的选科组合</p>
            <p className="text-sm text-gray-500">
              您的选择：{selection.firstChoice} + {selection.secondChoices.join(' + ')}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const difficultyInfo = getDifficultyDescription(currentCombination.difficulty)
  const popularityInfo = getPopularityDescription(currentCombination.popularityRank)

  return (
    <div className={cn("space-y-6", className)}>
      {/* 组合概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-orange-500" />
            {currentCombination.name}
            <Badge variant="outline" className="bg-orange-50 text-orange-700">
              第{currentCombination.popularityRank}热门
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            {currentCombination.description}
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 专业覆盖率 */}
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <PieChart className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">
                {currentCombination.majorCoverage}%
              </div>
              <div className="text-sm text-gray-600">专业覆盖率</div>
            </div>

            {/* 学习难度 */}
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <BarChart3 className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-yellow-600">
                {currentCombination.difficulty}/5
              </div>
              <div className="text-sm text-gray-600">学习难度</div>
            </div>

            {/* 热门程度 */}
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Star className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">
                #{currentCombination.popularityRank}
              </div>
              <div className="text-sm text-gray-600">热门排名</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 详细分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 优势分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              组合优势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {currentCombination.advantages.map((advantage, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{advantage}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 劣势分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              需要注意
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {currentCombination.disadvantages.map((disadvantage, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{disadvantage}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 竞争分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-blue-500" />
            竞争分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 性别比例 */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">性别分布</h4>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>男生</span>
                    <span>{currentCombination.genderRatio.male}%</span>
                  </div>
                  <Progress 
                    value={currentCombination.genderRatio.male} 
                    className="h-2"
                  />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>女生</span>
                    <span>{currentCombination.genderRatio.female}%</span>
                  </div>
                  <Progress 
                    value={currentCombination.genderRatio.female} 
                    className="h-2"
                  />
                </div>
              </div>
            </div>

            {/* 竞争程度 */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">竞争程度</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">学习难度</span>
                  <Badge className={difficultyInfo.color}>
                    {difficultyInfo.text}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">热门程度</span>
                  <Badge className={popularityInfo.color}>
                    {popularityInfo.text}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">专业覆盖</span>
                  <Badge className={
                    currentCombination.majorCoverage >= 90 
                      ? 'text-green-600 bg-green-50'
                      : currentCombination.majorCoverage >= 70
                      ? 'text-yellow-600 bg-yellow-50'
                      : 'text-red-600 bg-red-50'
                  }>
                    {currentCombination.majorCoverage >= 90 ? '很广' : 
                     currentCombination.majorCoverage >= 70 ? '较广' : '有限'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 其他热门组合对比 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-purple-500" />
            热门组合对比
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {popularCombinations.slice(0, 3).map((combo, index) => {
              const isCurrent = combo.id === currentCombination.id
              return (
                <div 
                  key={combo.id}
                  className={cn(
                    "p-4 rounded-lg border-2 transition-all",
                    isCurrent 
                      ? "border-orange-500 bg-orange-50" 
                      : "border-gray-200 bg-gray-50"
                  )}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        #{combo.popularityRank}
                      </Badge>
                      <span className="font-medium">{combo.name}</span>
                      {isCurrent && (
                        <Badge className="bg-orange-500 text-white">当前选择</Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      覆盖率 {combo.majorCoverage}%
                    </div>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>难度: {combo.difficulty}/5</span>
                    <span>男女比: {combo.genderRatio.male}:{combo.genderRatio.female}</span>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

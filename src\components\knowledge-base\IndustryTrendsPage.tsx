import { useState, useEffect } from "react"
import { ArrowLeft, TrendingUp, Search, Filter, Eye, Calendar, ChevronRight, Loader2, AlertCircle } from "lucide-react"
import { Button } from "../ui/button"
import {
  getIndustryTrendsList,
  formatTimestamp,
  type IndustryTrendItem
} from "../../services/industryTrendsApi"

interface IndustryTrendsPageProps {
  onBack: () => void
  onNavigateToDetail?: (trend: IndustryTrendItem) => void
}

export function IndustryTrendsPage({ onBack, onNavigateToDetail }: IndustryTrendsPageProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [trends, setTrends] = useState<IndustryTrendItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const pageSize = 20

  // 加载行业趋势数据
  const loadTrends = async (page: number = 1) => {
    setLoading(true)
    setError(null)

    try {
      const response = await getIndustryTrendsList(page, pageSize)
      setTrends(response.data.list)
      setTotalCount(response.data.total)
      console.log('✅ 成功加载行业趋势数据:', response.data.list.length, '条')
    } catch (err) {
      console.error('❌ 加载行业趋势失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
      setTrends([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadTrends(currentPage)
  }, [currentPage])

  // 处理搜索和筛选
  const filteredTrends = trends.filter(trend => {
    const matchesSearch = trend.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         trend.ds.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesSearch
  })

  // 处理点击趋势项
  const handleTrendClick = (trend: IndustryTrendItem) => {
    if (onNavigateToDetail) {
      onNavigateToDetail(trend)
    }
  }

  // 分页处理
  const totalPages = Math.ceil(totalCount / pageSize)
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回知识库</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-800">行业趋势</h1>
              </div>
            </div>
            
            {/* 搜索框 */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索行业趋势..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            行业发展趋势分析
          </h2>
          <p className="text-gray-600">
            深入了解各行业发展动态，把握未来就业机会
          </p>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600 mr-3" />
            <span className="text-gray-600">加载行业趋势数据中...</span>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="text-center py-12">
            <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => loadTrends(currentPage)} variant="outline">
              重试
            </Button>
          </div>
        )}

        {/* 趋势列表 */}
        {!loading && !error && (
          <>
            <div className="space-y-6 mb-8">
              {filteredTrends.map((trend) => (
                <div
                  key={trend.id}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 cursor-pointer group"
                  onClick={() => handleTrendClick(trend)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="px-3 py-1 bg-blue-100 text-blue-700 text-sm font-medium rounded-full">
                          {trend.type}
                        </span>
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <Calendar className="w-4 h-4" />
                          <span>{formatTimestamp(trend.createTime)}</span>
                        </div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                        {trend.question}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {trend.ds}
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors ml-4" />
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        onClick={() => handlePageChange(page)}
                        className="w-10 h-10"
                      >
                        {page}
                      </Button>
                    )
                  })}
                </div>

                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            )}

            {/* 搜索结果为空时的提示 */}
            {filteredTrends.length === 0 && trends.length > 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">未找到相关趋势</h3>
                <p className="text-gray-600">请尝试其他关键词</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

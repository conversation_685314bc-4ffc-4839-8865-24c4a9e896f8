# 登录注册功能测试指南

## 测试环境
- 应用地址：http://localhost:5176
- 测试浏览器：Chrome、Firefox、Safari、Edge

## 测试场景

### 1. 未登录状态测试

#### 1.1 界面检查
- [ ] 访问首页，确认顶部导航显示"登录"和"注册"按钮
- [ ] 确认没有显示用户信息（地区、分数等）
- [ ] 确认"小程序"按钮正常显示

#### 1.2 按钮功能
- [ ] 点击"登录"按钮，跳转到登录页面
- [ ] 点击"注册"按钮，跳转到注册页面

### 2. 登录功能测试

#### 2.1 手机验证码登录
1. 点击"登录"按钮进入登录页面
2. 选择"手机登录"选项卡
3. 输入测试手机号：`13800138000`
4. 点击"获取验证码"按钮
   - [ ] 确认按钮变为倒计时状态（60s）
   - [ ] 确认倒计时结束后按钮恢复正常
5. 输入验证码：`123456`
6. 点击"立即登录"
   - [ ] 确认显示加载状态
   - [ ] 确认登录成功后跳转到首页
   - [ ] 确认顶部显示用户信息

#### 2.2 邮箱密码登录
1. 在登录页面选择"邮箱登录"选项卡
2. 输入测试邮箱：`<EMAIL>`
3. 输入测试密码：`123456`
4. 点击"立即登录"
   - [ ] 确认登录成功
   - [ ] 确认用户信息正确显示

#### 2.3 微信登录
1. 在登录页面点击"微信登录"按钮
   - [ ] 确认打开新窗口（模拟微信登录页面）

#### 2.4 表单验证测试
- [ ] 手机号格式验证（输入无效手机号）
- [ ] 邮箱格式验证（输入无效邮箱）
- [ ] 必填字段验证（空白提交）
- [ ] 错误信息显示正确

### 3. 注册功能测试

#### 3.1 手机号注册
1. 点击"注册"按钮进入注册页面
2. 选择"手机注册"选项卡
3. 填写表单：
   - 昵称：`测试用户`（可选）
   - 手机号：`13900139000`
   - 验证码：点击获取并输入`123456`
   - 密码：`123456`
   - 确认密码：`123456`
4. 点击"立即注册"
   - [ ] 确认注册成功
   - [ ] 确认自动登录并跳转首页

#### 3.2 邮箱注册
1. 选择"邮箱注册"选项卡
2. 填写表单：
   - 昵称：`邮箱用户`
   - 邮箱：`<EMAIL>`
   - 验证码：点击获取并输入`123456`
   - 密码：`123456`
   - 确认密码：`123456`
3. 点击"立即注册"
   - [ ] 确认注册成功

#### 3.3 用户名注册
1. 选择"账号注册"选项卡
2. 填写表单：
   - 昵称：`账号用户`
   - 用户名：`testuser123`
   - 密码：`123456`
   - 确认密码：`123456`
3. 点击"立即注册"
   - [ ] 确认注册成功（无需验证码）

#### 3.4 注册表单验证
- [ ] 密码长度验证（少于6位）
- [ ] 密码确认验证（两次密码不一致）
- [ ] 用户名格式验证（特殊字符）
- [ ] 手机号/邮箱格式验证

### 4. 已登录状态测试

#### 4.1 用户信息显示
- [ ] 顶部导航显示用户地区信息
- [ ] 显示用户选科信息
- [ ] 显示用户分数信息
- [ ] 显示通知铃铛
- [ ] 显示用户头像

#### 4.2 用户菜单
1. 鼠标悬停在用户头像上
   - [ ] 确认显示用户菜单
   - [ ] 显示用户昵称/用户名
   - [ ] 显示"退出登录"选项

#### 4.3 退出登录
1. 点击"退出登录"
   - [ ] 确认用户状态清除
   - [ ] 确认页面恢复到未登录状态
   - [ ] 确认本地存储清除

### 5. 页面切换测试

#### 5.1 登录注册页面切换
- [ ] 在登录页面点击"立即注册"链接
- [ ] 在注册页面点击"立即登录"链接
- [ ] 确认页面正确切换

#### 5.2 返回功能
- [ ] 在登录页面点击"返回"按钮
- [ ] 在注册页面点击"返回"按钮
- [ ] 确认正确返回首页

### 6. 响应式设计测试

#### 6.1 移动端适配
- [ ] 在移动设备上测试登录页面布局
- [ ] 在移动设备上测试注册页面布局
- [ ] 确认表单元素正确显示

#### 6.2 不同屏幕尺寸
- [ ] 测试平板尺寸显示
- [ ] 测试桌面端显示
- [ ] 确认响应式布局正常

### 7. 数据持久化测试

#### 7.1 页面刷新
1. 登录成功后刷新页面
   - [ ] 确认用户状态保持
   - [ ] 确认用户信息正确显示

#### 7.2 浏览器重启
1. 登录后关闭浏览器
2. 重新打开浏览器访问应用
   - [ ] 确认用户状态保持（如果token未过期）

### 8. 错误处理测试

#### 8.1 网络错误模拟
- [ ] 模拟网络断开情况
- [ ] 确认错误提示正确显示

#### 8.2 无效数据测试
- [ ] 输入错误的验证码
- [ ] 输入错误的密码
- [ ] 确认错误信息正确显示

## 测试数据

### 预设测试账号
```
手机登录：
- 手机号：13800138000
- 验证码：123456

邮箱登录：
- 邮箱：<EMAIL>
- 密码：123456
```

### 注册测试数据
```
手机注册：
- 手机号：13900139000
- 验证码：123456
- 密码：123456

邮箱注册：
- 邮箱：<EMAIL>
- 验证码：123456
- 密码：123456

用户名注册：
- 用户名：testuser123
- 密码：123456
```

## 预期结果

所有测试项目都应该通过，确保：
1. 用户可以正常登录和注册
2. 界面状态正确切换
3. 数据正确保存和显示
4. 错误处理机制正常
5. 响应式设计正常工作

## 问题报告

如发现问题，请记录：
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果
5. 浏览器版本
6. 设备信息

import { useState } from "react"
import { ArrowLeft, AlertTriangle, Search, TrendingDown, Users, AlertCircle, Info, ChevronRight, Filter } from "lucide-react"
import { Button } from "../ui/button"

interface CautiousMajorsPageProps {
  onBack: () => void
}

interface CautiousMajor {
  id: string
  name: string
  category: string
  description: string
  employmentRate: number
  averageSalary: number
  competitionLevel: "激烈" | "较激烈" | "一般"
  riskLevel: "高" | "中" | "低"
  challenges: string[]
  alternatives: string[]
  requirements: string[]
  marketSaturation: number
  reasons: string[]
}

export function CautiousMajorsPage({ onBack }: CautiousMajorsPageProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedRiskLevel, setSelectedRiskLevel] = useState("all")

  const categories = [
    { id: "all", name: "全部专业", count: 45 },
    { id: "literature", name: "文学", count: 12 },
    { id: "art", name: "艺术学", count: 8 },
    { id: "management", name: "管理学", count: 10 },
    { id: "science", name: "理学", count: 7 },
    { id: "engineering", name: "工学", count: 5 },
    { id: "others", name: "其他", count: 3 }
  ]

  const riskLevels = [
    { id: "all", name: "全部风险", count: 45 },
    { id: "高", name: "高风险", count: 15 },
    { id: "中", name: "中风险", count: 20 },
    { id: "低", name: "低风险", count: 10 }
  ]

  const cautiousMajors: CautiousMajor[] = [
    {
      id: "1",
      name: "新闻学",
      category: "文学",
      description: "培养具备系统的新闻理论知识与技能、宽广的文化与科学知识的新闻工作者。",
      employmentRate: 78.5,
      averageSalary: 6800,
      competitionLevel: "激烈",
      riskLevel: "高",
      marketSaturation: 85,
      challenges: [
        "传统媒体衰落，就业岗位减少",
        "新媒体竞争激烈，门槛相对较低",
        "工作压力大，加班频繁",
        "薪资水平相对较低"
      ],
      alternatives: [
        "网络与新媒体",
        "广告学",
        "传播学",
        "数字媒体技术"
      ],
      requirements: [
        "文字功底扎实",
        "敏锐的新闻嗅觉",
        "良好的沟通能力",
        "抗压能力强"
      ],
      reasons: [
        "媒体行业转型期，传统岗位需求下降",
        "自媒体兴起，专业门槛降低",
        "行业竞争激烈，淘汰率高"
      ]
    },
    {
      id: "2",
      name: "市场营销",
      category: "管理学",
      description: "培养具备管理、经济、法律、市场营销等方面的知识和能力的工商管理学科高级专门人才。",
      employmentRate: 82.3,
      averageSalary: 7200,
      competitionLevel: "激烈",
      riskLevel: "中",
      marketSaturation: 78,
      challenges: [
        "专业门槛相对较低，竞争激烈",
        "业绩压力大，收入不稳定",
        "需要较强的人际交往能力",
        "职业发展天花板明显"
      ],
      alternatives: [
        "数字营销",
        "电子商务",
        "国际商务",
        "工商管理"
      ],
      requirements: [
        "沟通表达能力强",
        "抗压能力强",
        "学习能力强",
        "团队协作能力"
      ],
      reasons: [
        "专业设置过于宽泛，缺乏核心竞争力",
        "市场饱和度高，就业竞争激烈",
        "薪资水平波动大，职业稳定性差"
      ]
    },
    {
      id: "3",
      name: "生物技术",
      category: "理学",
      description: "培养具备生命科学的基本理论和较系统的生物技术的基本理论、基本知识、基本技能的高级专门人才。",
      employmentRate: 75.8,
      averageSalary: 6500,
      competitionLevel: "较激烈",
      riskLevel: "高",
      marketSaturation: 72,
      challenges: [
        "产业化程度不高，就业面窄",
        "需要较高学历，本科就业困难",
        "研发周期长，见效慢",
        "薪资水平不如预期"
      ],
      alternatives: [
        "生物医学工程",
        "制药工程",
        "食品科学与工程",
        "临床医学"
      ],
      requirements: [
        "生物基础扎实",
        "实验操作能力强",
        "耐心细致",
        "持续学习能力"
      ],
      reasons: [
        "国内生物技术产业发展相对滞后",
        "高端岗位需要硕博学历",
        "投入产出比不理想"
      ]
    },
    {
      id: "4",
      name: "环境工程",
      category: "工学",
      description: "培养具备城市和现代工业环境保护的理论知识和工程设计能力的环境工程学科高级工程技术人才。",
      employmentRate: 79.2,
      averageSalary: 7800,
      competitionLevel: "较激烈",
      riskLevel: "中",
      marketSaturation: 68,
      challenges: [
        "就业主要依赖政策导向",
        "项目周期长，见效慢",
        "薪资增长空间有限",
        "工作环境相对艰苦"
      ],
      alternatives: [
        "给排水科学与工程",
        "环境科学",
        "化学工程与工艺",
        "安全工程"
      ],
      requirements: [
        "化学基础好",
        "环保意识强",
        "实践能力强",
        "责任心强"
      ],
      reasons: [
        "行业发展受政策影响较大",
        "技术更新换代快，需持续学习",
        "职业发展路径相对单一"
      ]
    },
    {
      id: "5",
      name: "音乐表演",
      category: "艺术学",
      description: "培养具有音乐表演的专业知识和艺术素养，能够在专业文艺团体、艺术院校等相关部门从事表演、教学及研究工作的高级专门人才。",
      employmentRate: 68.5,
      averageSalary: 5200,
      competitionLevel: "激烈",
      riskLevel: "高",
      marketSaturation: 90,
      challenges: [
        "就业面极窄，竞争异常激烈",
        "收入不稳定，差距极大",
        "职业生涯短，年龄限制明显",
        "需要持续投入大量时间和金钱"
      ],
      alternatives: [
        "音乐学",
        "音乐教育",
        "录音艺术",
        "数字媒体艺术"
      ],
      requirements: [
        "音乐天赋突出",
        "专业技能精湛",
        "心理素质强",
        "家庭经济支持"
      ],
      reasons: [
        "市场需求有限，供大于求",
        "成功率极低，风险极高",
        "职业发展不确定性大"
      ]
    }
  ]

  const filteredMajors = cautiousMajors.filter(major => {
    const matchesSearch = major.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         major.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         major.challenges.some(challenge => challenge.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || 
                           major.category === categories.find(cat => cat.id === selectedCategory)?.name
    
    const matchesRisk = selectedRiskLevel === "all" || major.riskLevel === selectedRiskLevel
    
    return matchesSearch && matchesCategory && matchesRisk
  })

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "高":
        return "bg-red-100 text-red-700 border-red-200"
      case "中":
        return "bg-yellow-100 text-yellow-700 border-yellow-200"
      case "低":
        return "bg-green-100 text-green-700 border-green-200"
      default:
        return "bg-gray-100 text-gray-700 border-gray-200"
    }
  }

  const getCompetitionColor = (level: string) => {
    switch (level) {
      case "激烈":
        return "text-red-600"
      case "较激烈":
        return "text-yellow-600"
      case "一般":
        return "text-green-600"
      default:
        return "text-gray-600"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回知识库</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-6 h-6 text-red-600" />
                <h1 className="text-xl font-bold text-gray-800">慎选专业</h1>
              </div>
            </div>
            
            {/* 搜索框 */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索需要慎重考虑的专业..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 警告提示 */}
        <div className="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-6 h-6 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-red-800 mb-2">重要提醒</h3>
              <p className="text-red-700 leading-relaxed">
                以下专业并非完全不可选择，而是需要考生和家长更加谨慎地评估个人兴趣、能力、家庭条件和就业预期。
                每个专业都有其价值和发展空间，关键在于是否适合自己。建议在选择前充分了解专业特点、就业现状和发展前景。
              </p>
            </div>
          </div>
        </div>

        <div className="flex gap-8">
          {/* 左侧筛选 */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 sticky top-24">
              {/* 分类筛选 */}
              <div className="mb-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Filter className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold text-gray-800">专业分类</h3>
                </div>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center justify-between ${
                        selectedCategory === category.id
                          ? "bg-red-100 text-red-700 font-medium"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      <span>{category.name}</span>
                      <span className="text-sm text-gray-500">({category.count})</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* 风险等级筛选 */}
              <div>
                <h3 className="font-semibold text-gray-800 mb-4">风险等级</h3>
                <div className="space-y-2">
                  {riskLevels.map((level) => (
                    <button
                      key={level.id}
                      onClick={() => setSelectedRiskLevel(level.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center justify-between ${
                        selectedRiskLevel === level.id
                          ? "bg-red-100 text-red-700 font-medium"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      <span>{level.name}</span>
                      <span className="text-sm text-gray-500">({level.count})</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 右侧专业列表 */}
          <div className="flex-1">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                需要慎重考虑的专业
              </h2>
              <p className="text-gray-600">
                了解这些专业的挑战和风险，做出明智的选择
              </p>
            </div>

            <div className="space-y-6">
              {filteredMajors.map((major) => (
                <div
                  key={major.id}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getRiskColor(major.riskLevel)}`}>
                          {major.riskLevel}风险
                        </span>
                        <span className="px-3 py-1 bg-gray-100 text-gray-700 text-sm font-medium rounded-full">
                          {major.category}
                        </span>
                        <div className="flex items-center space-x-1">
                          <Users className="w-4 h-4 text-gray-500" />
                          <span className={`text-sm font-medium ${getCompetitionColor(major.competitionLevel)}`}>
                            竞争{major.competitionLevel}
                          </span>
                        </div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-2">
                        {major.name}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        {major.description}
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 ml-4" />
                  </div>

                  {/* 关键指标 */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-3 bg-red-50 rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">就业率</div>
                      <div className="text-lg font-bold text-red-600">{major.employmentRate}%</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">平均薪资</div>
                      <div className="text-lg font-bold text-orange-600">¥{major.averageSalary.toLocaleString()}</div>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">市场饱和度</div>
                      <div className="text-lg font-bold text-yellow-600">{major.marketSaturation}%</div>
                    </div>
                  </div>

                  {/* 主要挑战 */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                      <AlertTriangle className="w-4 h-4 text-red-500 mr-1" />
                      主要挑战
                    </h4>
                    <div className="space-y-2">
                      {major.challenges.map((challenge, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-sm text-gray-700">{challenge}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 替代专业推荐 */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                      <Info className="w-4 h-4 text-blue-500 mr-1" />
                      替代专业推荐
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {major.alternatives.map((alternative, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full"
                        >
                          {alternative}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* 慎选原因 */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">慎选原因</h4>
                    <div className="space-y-1">
                      {major.reasons.map((reason, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-sm text-gray-600">{reason}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 搜索结果为空时的提示 */}
            {filteredMajors.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">未找到相关专业</h3>
                <p className="text-gray-600">请尝试其他关键词或调整筛选条件</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

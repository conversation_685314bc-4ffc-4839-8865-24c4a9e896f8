// 用户信息类型
export interface User {
  id: string
  username?: string
  email?: string
  phone?: string
  avatar?: string
  nickname?: string
  province?: string
  subjects?: string[]
  score?: number
  ranking?: number
  createdAt: string
  updatedAt: string
}

// 登录方式类型
export type LoginMethod = 'phone' | 'email' | 'wechat'

// 注册方式类型
export type RegisterMethod = 'phone' | 'email' | 'username'

// 登录请求数据
export interface LoginRequest {
  method: LoginMethod
  phone?: string
  email?: string
  password?: string
  verificationCode?: string
  wechatCode?: string
}

// 注册请求数据
export interface RegisterRequest {
  method: RegisterMethod
  username?: string
  email?: string
  phone?: string
  password: string
  confirmPassword: string
  verificationCode?: string
  nickname?: string
}

// 验证码请求数据
export interface VerificationCodeRequest {
  type: 'login' | 'register'
  phone?: string
  email?: string
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
}

// 登录响应数据
export interface LoginResponse {
  user: User
  token: string
  refreshToken?: string
}

// 认证上下文类型
export interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (data: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => void
  sendVerificationCode: (data: VerificationCodeRequest) => Promise<void>
  updateUser: (data: Partial<User>) => Promise<void>
}

// 微信登录相关类型
export interface WechatLoginConfig {
  appId: string
  redirectUri: string
  scope: string
  state: string
}

// 表单验证错误类型
export interface FormErrors {
  [key: string]: string
}

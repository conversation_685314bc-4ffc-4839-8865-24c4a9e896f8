// 热门专业API服务
export interface PopularMajorItem {
  id: number
  content: string | null
  type: string
  parentType: string
  question: string
  version: string | null
  createTime: number | null
  star: string | null
  ds: string
}

export interface PopularMajorDetailItem {
  id: number
  questionId: number
  content: string
  type: string
  parentType: string
  question: string | null
  version: string | null
  createTime: number
  star: string | null
  ds: string
  dsCus: string
}

export interface PopularMajorsListResponse {
  code: number
  data: {
    list: PopularMajorItem[]
    total: number
  }
  msg: string
}

export interface PopularMajorDetailResponse {
  code: number
  data: {
    list: PopularMajorDetailItem[]
    total: number
  }
  msg: string
}

// API配置
const API_BASE_URL = 'https://m.kefeichangduo.top'

/**
 * 获取热门专业列表
 */
export async function getPopularMajorsList(
  pageNo: number = 1,
  pageSize: number = 100
): Promise<PopularMajorsListResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question/page?parentType=3&pageNo=${pageNo}&pageSize=${pageSize}`
    
    console.log('🔍 请求热门专业列表:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: PopularMajorsListResponse = await response.json()
    console.log('✅ 热门专业列表响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取热门专业列表失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 获取热门专业详情
 */
export async function getPopularMajorDetail(
  questionId: number,
  version: string = '',
  dsCus: string = ''
): Promise<PopularMajorDetailResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question-content/page?questionId=${questionId}&version=${version}&dsCus=${encodeURIComponent(dsCus)}`
    
    console.log('🔍 请求热门专业详情:', url)
    console.log('📋 请求参数:', { questionId, version, dsCus })
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: PopularMajorDetailResponse = await response.json()
    console.log('✅ 热门专业详情响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取热门专业详情失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 格式化时间戳为可读日期
 */
export function formatTimestamp(timestamp: number | null): string {
  if (!timestamp) return '未知时间'
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 根据专业类型获取分类信息
 */
export function getCategoryInfo(type: string): { name: string; color: string; bgColor: string } {
  const categoryMap: Record<string, { name: string; color: string; bgColor: string }> = {
    '医学类': { name: '医学类', color: 'text-red-700', bgColor: 'bg-red-100' },
    '工学类': { name: '工学类', color: 'text-blue-700', bgColor: 'bg-blue-100' },
    '理学类': { name: '理学类', color: 'text-green-700', bgColor: 'bg-green-100' },
    '经济学类': { name: '经济学类', color: 'text-yellow-700', bgColor: 'bg-yellow-100' },
    '管理学类': { name: '管理学类', color: 'text-purple-700', bgColor: 'bg-purple-100' },
    '文学类': { name: '文学类', color: 'text-pink-700', bgColor: 'bg-pink-100' },
    '法学类': { name: '法学类', color: 'text-indigo-700', bgColor: 'bg-indigo-100' },
    '教育学类': { name: '教育学类', color: 'text-teal-700', bgColor: 'bg-teal-100' },
    '艺术学类': { name: '艺术学类', color: 'text-orange-700', bgColor: 'bg-orange-100' },
    '农学类': { name: '农学类', color: 'text-lime-700', bgColor: 'bg-lime-100' },
    '历史学类': { name: '历史学类', color: 'text-amber-700', bgColor: 'bg-amber-100' },
    '哲学类': { name: '哲学类', color: 'text-slate-700', bgColor: 'bg-slate-100' },
    '兵器类': { name: '兵器类', color: 'text-gray-700', bgColor: 'bg-gray-100' },
    '仪器类': { name: '仪器类', color: 'text-cyan-700', bgColor: 'bg-cyan-100' }
  }
  
  return categoryMap[type] || { name: type, color: 'text-gray-700', bgColor: 'bg-gray-100' }
}

/**
 * 获取所有分类统计
 */
export function getCategoryStats(majorItems: PopularMajorItem[]): Array<{ id: string; name: string; count: number }> {
  const categoryCount: Record<string, number> = {}
  
  majorItems.forEach(item => {
    categoryCount[item.type] = (categoryCount[item.type] || 0) + 1
  })
  
  const categories = [
    { id: 'all', name: '全部专业', count: majorItems.length }
  ]
  
  Object.entries(categoryCount).forEach(([type, count]) => {
    categories.push({
      id: type,
      name: type,
      count
    })
  })
  
  return categories
}

/**
 * 专业详情查看选项
 */
export const DETAIL_VIEW_OPTIONS = [
  { id: 'overview', name: '专业概述', version: '', dsCus: '' },
  { id: 'score-segments', name: '各分段推荐', version: '2', dsCus: '2' },
  { id: 'career-prospects', name: '就业前景', version: '3', dsCus: '3' }
]

import { useState } from 'react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import type { VolunteerUniversity, VolunteerMajor, AdmissionLevel } from '../../types/volunteer'
import {
  Search,
  Plus,
  Check,
  MapPin,
  Users,
  ChevronDown,
  ChevronUp,
  BookOpen,
  AlertTriangle
} from 'lucide-react'

interface UniversityListViewProps {
  universities: VolunteerUniversity[]
  selectedUniversities: string[]
  userScore?: number
  userRanking?: number
  admissionLevel: AdmissionLevel
  searchKeyword: string
  onUniversitySelect: (university: VolunteerUniversity, major: VolunteerMajor) => void
  onSearchChange: (keyword: string) => void
  className?: string
}

export function UniversityListView({
  universities,
  selectedUniversities,
  userScore,
  userRanking,
  admissionLevel,
  searchKeyword,
  onUniversitySelect,
  onSearchChange,
  className
}: UniversityListViewProps) {
  const [expandedUniversities, setExpandedUniversities] = useState<Set<string>>(new Set())

  // 分析录取概率
  const analyzeUniversityAdmission = (university: VolunteerUniversity): 'rush' | 'stable' | 'safe' | null => {
    if (!userScore || !userRanking || !university.scores.length) return null

    const latestScore = university.scores[0]
    const scoreDiff = userScore - latestScore.minScore
    const rankingDiff = userRanking - latestScore.ranking

    if (scoreDiff >= 20 && rankingDiff <= -1000) {
      return 'safe'
    } else if (scoreDiff >= 10 && rankingDiff <= -500) {
      return 'stable'
    } else if (scoreDiff >= -10 && rankingDiff <= 500) {
      return 'rush'
    }
    return null
  }

  // 根据等级筛选院校
  const filteredUniversities = universities.filter(university => {
    if (admissionLevel === 'all') return true
    
    const suggestion = analyzeUniversityAdmission(university)
    return suggestion === admissionLevel
  })

  // 切换院校展开状态
  const toggleUniversity = (universityId: string) => {
    const newExpanded = new Set(expandedUniversities)
    if (newExpanded.has(universityId)) {
      newExpanded.delete(universityId)
    } else {
      newExpanded.add(universityId)
    }
    setExpandedUniversities(newExpanded)
  }

  // 获取院校类型颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case '985':
        return 'bg-red-100 text-red-800 border-red-200'
      case '211':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case '双一流':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case '普通本科':
        return 'bg-green-100 text-green-800 border-green-200'
      case '民办':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取建议标签样式
  const getSuggestionStyle = (suggestion: string | null) => {
    switch (suggestion) {
      case 'safe':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'stable':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'rush':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getSuggestionText = (suggestion: string | null) => {
    switch (suggestion) {
      case 'safe':
        return '保'
      case 'stable':
        return '稳'
      case 'rush':
        return '冲'
      default:
        return '未知'
    }
  }

  return (
    <div className={className}>
      {/* 搜索栏 */}
      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="搜索院校名称..."
            value={searchKeyword}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* 院校列表 */}
      <div className="space-y-2">
        {filteredUniversities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>没有找到符合条件的院校</p>
          </div>
        ) : (
          filteredUniversities.map((university) => {
            const isExpanded = expandedUniversities.has(university.id)
            const suggestion = analyzeUniversityAdmission(university)
            const latestScore = university.scores[0]

            return (
              <div
                key={university.id}
                className="border rounded-lg bg-white hover:shadow-md transition-all duration-200"
              >
                {/* 院校基本信息 */}
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {university.ranking?.national && (
                          <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                            #{university.ranking.national}
                          </Badge>
                        )}
                        <h3 className="font-semibold text-gray-900 text-lg">
                          {university.name}
                        </h3>
                        {university.shortName && (
                          <span className="text-sm text-gray-500">({university.shortName})</span>
                        )}
                        {suggestion && (
                          <Badge className={getSuggestionStyle(suggestion)}>
                            {getSuggestionText(suggestion)}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={getTypeColor(university.type)}>
                          {university.type}
                        </Badge>
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          {university.category}
                        </Badge>
                        {!university.isPublic && (
                          <Badge variant="outline" className="bg-purple-50 text-purple-700">
                            民办
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          <span>{university.location.province} · {university.location.city}</span>
                        </div>
                        {university.admissionPlan && (
                          <div className="flex items-center gap-1">
                            <Users className="w-4 h-4" />
                            <span>招生 {university.admissionPlan.total}人</span>
                          </div>
                        )}
                      </div>

                      {/* 分数信息 */}
                      {latestScore && (
                        <div className="mt-2 flex items-center gap-6 text-sm">
                          <div>
                            <span className="text-gray-600">最低分: </span>
                            <span className="font-semibold text-gray-900">{latestScore.minScore}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">平均分: </span>
                            <span className="font-semibold text-gray-900">{latestScore.avgScore}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">位次: </span>
                            <span className="font-semibold text-gray-900">{latestScore.ranking}</span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 展开按钮 */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleUniversity(university.id)}
                      className="ml-4"
                    >
                      {isExpanded ? (
                        <>
                          <ChevronUp className="w-4 h-4 mr-1" />
                          收起
                        </>
                      ) : (
                        <>
                          <ChevronDown className="w-4 h-4 mr-1" />
                          查看专业
                        </>
                      )}
                    </Button>
                  </div>

                  {/* 分析提醒 */}
                  {suggestion === 'rush' && (
                    <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-center gap-2 text-sm text-orange-800">
                        <AlertTriangle className="w-4 h-4" />
                        <span>该院校录取难度较大，建议作为冲刺志愿</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* 可填专业列表 */}
                {isExpanded && university.availableMajors && (
                  <div className="border-t border-gray-200 p-4 bg-gray-50">
                    <h4 className="font-medium text-gray-900 mb-3">可填报专业</h4>
                    <div className="space-y-2">
                      {university.availableMajors.map((major) => (
                        <div
                          key={major.id}
                          className="flex items-center justify-between p-3 bg-white rounded-lg border"
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-gray-900">{major.name}</span>
                              <span className="text-sm text-gray-500">({major.code})</span>
                            </div>
                            <div className="text-sm text-gray-600">{major.category}</div>
                          </div>
                          <Button
                            size="sm"
                            onClick={() => onUniversitySelect(university, major)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Plus className="w-4 h-4 mr-1" />
                            选择
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )
          })
        )}
      </div>

      {/* 统计信息 */}
      <div className="mt-4 text-sm text-gray-500 text-center">
        共找到 {filteredUniversities.length} 所院校
      </div>
    </div>
  )
}

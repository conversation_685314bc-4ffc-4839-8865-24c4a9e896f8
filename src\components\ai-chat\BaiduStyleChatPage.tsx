import { useState, useRef, useEffect, useCallback } from 'react'
import { But<PERSON> } from '../ui/button'
import { 
  ArrowLeft, 
  Send, 
  Plus,
  MessageSquare,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  <PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { aiService } from '../../services/aiService'
import { MarkdownRenderer } from '../ui/markdown-renderer'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  thinking?: string
  isThinkingCollapsed?: boolean
  followUpQueries?: string[]
  isStreaming?: boolean
}

interface Conversation {
  id: string
  title: string
  messages: Message[]
  timestamp: Date
}

interface BaiduStyleChatPageProps {
  onBack: () => void
}

export function BaiduStyleChatPage({ onBack }: BaiduStyleChatPageProps) {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [apiKeyValid, setApiKeyValid] = useState<boolean | null>(null)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const [aiConversationId, setAiConversationId] = useState<string | null>(null) // 存储AI服务的对话ID
  const [isInitialized, setIsInitialized] = useState(false) // 防止重复初始化
  const initRef = useRef(false) // 防止StrictMode重复初始化

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 获取当前对话
  const currentConversation = conversations.find(conv => conv.id === currentConversationId)
  const messages = currentConversation?.messages || []

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 创建新对话
  const createNewConversation = useCallback(() => {
    // 重置AI对话ID，让新对话创建新的AI会话
    setAiConversationId(null)

    const welcomeMessage: Message = {
      id: Date.now().toString(),
      role: 'assistant',
      content: `**欢迎使用智能问答助手！** 🎓

我是您的高考志愿填报专业顾问，可以为您提供：

• **专业选择指导** - 根据兴趣和能力推荐适合的专业
• **院校信息查询** - 详细的大学信息和录取要求
• **志愿填报策略** - 平行志愿、梯度设置等技巧
• **就业前景分析** - 各专业的发展趋势和薪资水平
• **政策解读** - 最新的高考政策和录取规则

请直接在下方输入您的问题，我会为您提供专业、详细的解答！`,
      timestamp: new Date(),
      isThinkingCollapsed: true,
      followUpQueries: [
        '如何选择适合自己的专业？',
        '985和211大学有什么区别？',
        '什么是平行志愿？如何填报？',
        '计算机专业的就业前景如何？'
      ]
    }

    const newConversation: Conversation = {
      id: Date.now().toString(),
      title: '新对话',
      messages: [welcomeMessage],
      timestamp: new Date()
    }

    setConversations(prev => [newConversation, ...prev])
    setCurrentConversationId(newConversation.id)
  }, [])



  // 初始化时自动创建新对话
  useEffect(() => {
    if (initRef.current) return // 防止StrictMode重复执行
    initRef.current = true
    
    // 直接设置API密钥为有效状态
    setApiKeyValid(true)
    // 创建新对话
    createNewConversation()
    // 标记已初始化
    setIsInitialized(true)
  }, []) // 空依赖数组，确保只执行一次

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || apiKeyValid === false) return

    // 如果没有当前对话，创建一个新的
    let conversationId = currentConversationId
    if (!conversationId) {
      const newConv: Conversation = {
        id: Date.now().toString(),
        title: '新对话',
        messages: [],
        timestamp: new Date()
      }
      setConversations(prev => [newConv, ...prev])
      setCurrentConversationId(newConv.id)
      conversationId = newConv.id
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    }

    const messageContent = inputValue.trim()
    setInputValue('')

    // 添加用户消息
    setConversations(prev => prev.map(conv => 
      conv.id === conversationId 
        ? {
            ...conv,
            messages: [...conv.messages, userMessage],
            title: conv.messages.length === 0 ? messageContent.slice(0, 20) + (messageContent.length > 20 ? '...' : '') : conv.title
          }
        : conv
    ))

    setIsLoading(true)

    try {
      // 创建助手消息
      const assistantMessageId = (Date.now() + 1).toString()
      const assistantMessage: Message = {
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        thinking: '',
        isThinkingCollapsed: false,
        timestamp: new Date(),
        isStreaming: true,
        followUpQueries: []
      }

      // 添加空的助手消息
      setConversations(prev => prev.map(conv => 
        conv.id === conversationId 
          ? { ...conv, messages: [...conv.messages, assistantMessage] }
          : conv
      ))
      setStreamingMessageId(assistantMessageId)

      // 调用AI服务
      const aiResponse = await aiService.askQuestion(
        messageContent,
        aiConversationId || undefined, // 使用验证过程中创建的对话ID
        (streamText: string, type: 'thinking' | 'answer' | 'followup', data?: any) => {
          setConversations(prev => prev.map(conv => 
            conv.id === conversationId 
              ? {
                  ...conv,
                  messages: conv.messages.map(msg => 
                    msg.id === assistantMessageId 
                      ? {
                          ...msg,
                          thinking: type === 'thinking' ? streamText : msg.thinking,
                          content: type === 'answer' ? streamText : msg.content,
                          followUpQueries: type === 'followup' ? data?.follow_up_querys || [] : msg.followUpQueries
                        }
                      : msg
                  )
                }
              : conv
          ))
        }
      )

      // 完成流式更新
      setConversations(prev => prev.map(conv => 
        conv.id === conversationId 
          ? {
              ...conv,
              messages: conv.messages.map(msg => 
                msg.id === assistantMessageId 
                  ? {
                      ...msg,
                      content: aiResponse,
                      isStreaming: false,
                      isThinkingCollapsed: true // 思考过程收起
                    }
                  : msg
              )
            }
          : conv
      ))

    } catch (error) {
      console.error('AI API调用失败:', error)
      // 处理错误...
    } finally {
      setIsLoading(false)
      setStreamingMessageId(null)
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 自动调整输入框高度
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)

    // 重置高度并自动调整
    const textarea = e.target
    textarea.style.height = 'auto'
    const newHeight = Math.min(textarea.scrollHeight, 120)
    textarea.style.height = newHeight + 'px'
  }

  // 复制消息内容
  const copyMessage = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 添加测试消息（用于演示Markdown渲染）
  const addTestMessage = () => {
    if (!currentConversationId) {
      createNewConversation()
    }

    const testUserMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: '我是上海2025届物理组考生，分数525分，请详细介绍一下平行志愿的填报策略。',
      timestamp: new Date()
    }

    const testAssistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: testMarkdownContent,
      thinking: testThinkingContent,
      isThinkingCollapsed: true,
      timestamp: new Date(),
      followUpQueries: testFollowUpQueries
    }

    const conversationId = currentConversationId || conversations[0]?.id
    if (conversationId) {
      setConversations(prev => prev.map(conv =>
        conv.id === conversationId
          ? {
              ...conv,
              messages: [...conv.messages, testUserMessage, testAssistantMessage],
              title: '平行志愿填报策略'
            }
          : conv
      ))
    }
  }

  // 切换思考过程显示
  const toggleThinking = (messageId: string) => {
    setConversations(prev => prev.map(conv => 
      conv.id === currentConversationId 
        ? {
            ...conv,
            messages: conv.messages.map(msg => 
              msg.id === messageId 
                ? { ...msg, isThinkingCollapsed: !msg.isThinkingCollapsed }
                : msg
            )
          }
        : conv
    ))
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* 左侧边栏 */}
      <div className={cn(
        "bg-white border-r border-gray-200 transition-all duration-300 flex flex-col",
        sidebarCollapsed ? "w-16" : "w-80"
      )}>
        {/* 侧边栏头部 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {!sidebarCollapsed && (
              <Button
                variant="outline"
                onClick={onBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                返回
              </Button>
            )}
            <Button
              onClick={createNewConversation}
              className="bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
              size={sidebarCollapsed ? "sm" : "default"}
            >
              <Plus className="w-4 h-4" />
              {!sidebarCollapsed && "新对话"}
            </Button>
          </div>
        </div>

        {/* 对话历史 */}
        {!sidebarCollapsed && (
          <div className="flex-1 overflow-y-auto p-2">
            <div className="space-y-2">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => setCurrentConversationId(conversation.id)}
                  className={cn(
                    "p-3 rounded-lg cursor-pointer transition-colors",
                    currentConversationId === conversation.id
                      ? "bg-blue-50 border border-blue-200"
                      : "hover:bg-gray-50"
                  )}
                >
                  <div className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium truncate">
                      {conversation.title}
                    </span>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {conversation.timestamp.toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 折叠按钮 */}
        <div className="p-2 border-t border-gray-200">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="w-full"
          >
            {sidebarCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 聊天头部 */}
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-800">智能问答助手</h1>
                <p className="text-sm text-gray-500">
                  {apiKeyValid === true ? "服务正常" : 
                   apiKeyValid === false ? "服务异常" : "检查中..."}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* API配置提示 */}
          {apiKeyValid === false && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-4xl mx-auto">
              <div className="flex items-center space-x-2 text-yellow-800">
                <Sparkles className="w-5 h-5" />
                <span className="font-medium">API配置提示</span>
              </div>
              <p className="text-sm text-yellow-700 mt-2">
                请配置有效的百度AppBuilder API密钥和应用ID以使用智能问答功能。
                <br />
                请在项目根目录创建 .env 文件并添加：
                <br />
                VITE_BAIDU_API_KEY=your_api_key_here
                <br />
                VITE_BAIDU_APP_ID=your_app_id_here
              </p>
            </div>
          )}

          <div className="max-w-4xl mx-auto space-y-6">
              {messages.map((message) => (
                <div key={message.id} className="space-y-4">
                  {/* 用户消息 */}
                  {message.role === 'user' && (
                    <div className="flex justify-end">
                      <div className="flex items-start space-x-3 max-w-[70%]">
                        <div className="bg-blue-500 text-white rounded-2xl px-4 py-3">
                          <p className="text-sm leading-relaxed">{message.content}</p>
                        </div>
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* AI消息 */}
                  {message.role === 'assistant' && (
                    <div className="flex justify-start">
                      <div className="flex items-start space-x-3 max-w-[80%]">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <Bot className="w-5 h-5 text-white" />
                        </div>
                        <div className="space-y-3">
                          {/* 思考过程 */}
                          {message.thinking && (
                            <div className="bg-gray-100 rounded-lg p-3">
                              <div 
                                className="flex items-center justify-between cursor-pointer"
                                onClick={() => toggleThinking(message.id)}
                              >
                                <span className="text-xs text-gray-500 flex items-center gap-1">
                                  <Sparkles className="w-3 h-3" />
                                  思考过程
                                </span>
                                {message.isThinkingCollapsed ? 
                                  <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                                  <ChevronUp className="w-4 h-4 text-gray-400" />
                                }
                              </div>
                              {!message.isThinkingCollapsed && (
                                <div className="mt-2">
                                  <MarkdownRenderer
                                    content={message.thinking || ''}
                                    isStreaming={message.isStreaming}
                                    className="text-xs text-gray-600"
                                  />
                                </div>
                              )}
                            </div>
                          )}

                          {/* 回答内容 */}
                          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 relative group">
                            <MarkdownRenderer
                              content={message.content}
                              isStreaming={message.isStreaming}
                              className="text-gray-700"
                            />

                            {/* 复制按钮 */}
                            {message.content && !message.isStreaming && (
                              <button
                                onClick={() => copyMessage(message.content, message.id)}
                                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1.5 rounded-md hover:bg-gray-100"
                                title="复制内容"
                              >
                                {copiedMessageId === message.id ? (
                                  <Check className="w-4 h-4 text-green-500" />
                                ) : (
                                  <Copy className="w-4 h-4 text-gray-400" />
                                )}
                              </button>
                            )}
                          </div>

                          {/* 追问建议 */}
                          {message.followUpQueries && message.followUpQueries.length > 0 && (
                            <div className="space-y-2">
                              <p className="text-xs text-gray-500">相关问题：</p>
                              <div className="flex flex-wrap gap-2">
                                {message.followUpQueries.map((query, index) => (
                                  <button
                                    key={index}
                                    onClick={() => setInputValue(query)}
                                    className="text-xs px-3 py-2 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-full border border-blue-200 transition-colors"
                                  >
                                    {query}
                                  </button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </div>

        {/* 输入区域 */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="relative bg-white rounded-lg border border-gray-300 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all duration-200">
              <textarea
                ref={inputRef}
                value={inputValue}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                placeholder="请输入您的问题..."
                className="w-full resize-none bg-transparent px-4 py-3 pr-14 text-sm leading-relaxed min-h-[48px] max-h-[120px] overflow-y-auto border-0 focus:outline-none focus:ring-0"
                rows={1}
                disabled={isLoading || apiKeyValid === false}
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading || apiKeyValid === false}
                className={cn(
                  "absolute right-2 bottom-2 p-2 rounded-full flex items-center justify-center transition-all duration-200 shadow-sm",
                  !inputValue.trim() || isLoading || apiKeyValid === false
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed scale-90 opacity-60"
                    : "bg-blue-500 hover:bg-blue-600 text-white hover:scale-110 active:scale-95 shadow-md hover:shadow-lg"
                )}
                size="sm"
                title={inputValue.trim() ? "发送消息 (Enter)" : "请输入内容"}
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </Button>
            </div>

            {/* 快捷键提示 */}
            <div className="flex justify-between items-center mt-2 text-xs text-gray-400">
              <span>支持Markdown格式</span>
              <span>Enter发送 • Shift+Enter换行</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

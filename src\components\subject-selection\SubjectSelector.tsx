import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import type { Subject, UserSelection, SubjectInfo } from './types'
import { subjectData } from './data'
import { 
  BookOpen, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Lightbulb,
  Target
} from 'lucide-react'

interface SubjectSelectorProps {
  selection: UserSelection
  onSelectionChange: (selection: UserSelection) => void
  className?: string
}

export function SubjectSelector({ selection, onSelectionChange, className }: SubjectSelectorProps) {
  const [showInfo, setShowInfo] = useState<Subject | null>(null)

  // 获取科目信息
  const getSubjectInfo = (subjectId: Subject): SubjectInfo => {
    return subjectData.find(s => s.id === subjectId)!
  }

  // 处理首选科目选择
  const handleFirstChoiceSelect = (subject: '物理' | '历史') => {
    const newSelection: UserSelection = {
      ...selection,
      firstChoice: subject,
      isComplete: selection.secondChoices.length === 2
    }
    onSelectionChange(newSelection)
  }

  // 处理再选科目选择
  const handleSecondChoiceSelect = (subject: Subject) => {
    let newSecondChoices = [...selection.secondChoices]
    
    if (newSecondChoices.includes(subject)) {
      // 取消选择
      newSecondChoices = newSecondChoices.filter(s => s !== subject)
    } else if (newSecondChoices.length < 2) {
      // 添加选择
      newSecondChoices.push(subject)
    } else {
      // 替换最后一个选择
      newSecondChoices[1] = subject
    }

    const newSelection: UserSelection = {
      ...selection,
      secondChoices: newSecondChoices,
      isComplete: selection.firstChoice !== null && newSecondChoices.length === 2
    }
    onSelectionChange(newSelection)
  }

  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'hard': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const firstChoiceSubjects: ('物理' | '历史')[] = ['物理', '历史']
  const secondChoiceSubjects: Subject[] = ['化学', '生物', '政治', '地理']

  return (
    <div className={cn("space-y-6", className)}>
      {/* 选择进度 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-orange-500" />
            选科进度
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                selection.firstChoice ? "bg-green-500" : "bg-gray-300"
              )} />
              <span className="text-sm">首选科目</span>
              {selection.firstChoice && (
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  {selection.firstChoice}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                selection.secondChoices.length === 2 ? "bg-green-500" : "bg-gray-300"
              )} />
              <span className="text-sm">再选科目</span>
              <span className="text-xs text-gray-500">
                ({selection.secondChoices.length}/2)
              </span>
              {selection.secondChoices.map(subject => (
                <Badge key={subject} variant="outline" className="bg-blue-50 text-blue-700">
                  {subject}
                </Badge>
              ))}
            </div>
          </div>
          {selection.isComplete && (
            <div className="mt-3 flex items-center gap-2 text-green-600">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm font-medium">选科完成！</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 首选科目选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-blue-500" />
            首选科目
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              2选1
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            物理或历史，必须选择其中一科
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {firstChoiceSubjects.map(subject => {
              const subjectInfo = getSubjectInfo(subject)
              const isSelected = selection.firstChoice === subject
              
              return (
                <div
                  key={subject}
                  className={cn(
                    "relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200",
                    isSelected 
                      ? "border-orange-500 bg-orange-50" 
                      : "border-gray-200 hover:border-orange-300 hover:bg-orange-25"
                  )}
                  onClick={() => handleFirstChoiceSelect(subject)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-900">{subject}</h3>
                        <Badge className={getDifficultyColor(subjectInfo.difficulty)}>
                          {subjectInfo.difficulty === 'easy' ? '简单' : 
                           subjectInfo.difficulty === 'medium' ? '中等' : '困难'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        {subjectInfo.description}
                      </p>
                      <div className="space-y-2">
                        <div>
                          <span className="text-xs font-medium text-gray-700">适合人群：</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {subjectInfo.suitableFor.slice(0, 2).map(trait => (
                              <Badge key={trait} variant="outline" className="text-xs">
                                {trait}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      {isSelected && (
                        <CheckCircle className="w-6 h-6 text-orange-500" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowInfo(showInfo === subject ? null : subject)
                        }}
                      >
                        <Info className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {showInfo === subject && (
                    <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                      <div className="space-y-2">
                        <div>
                          <span className="text-xs font-medium text-gray-700">学科特点：</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {subjectInfo.characteristics.map(char => (
                              <Badge key={char} variant="outline" className="text-xs">
                                {char}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-gray-700">相关专业：</span>
                          <p className="text-xs text-gray-600 mt-1">
                            {subjectInfo.relatedMajors.join('、')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 再选科目选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-purple-500" />
            再选科目
            <Badge variant="outline" className="bg-purple-50 text-purple-700">
              4选2
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            从化学、生物、政治、地理中选择2科
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {secondChoiceSubjects.map(subject => {
              const subjectInfo = getSubjectInfo(subject)
              const isSelected = selection.secondChoices.includes(subject)
              const canSelect = !isSelected && selection.secondChoices.length < 2
              
              return (
                <div
                  key={subject}
                  className={cn(
                    "relative p-4 border-2 rounded-lg transition-all duration-200",
                    isSelected 
                      ? "border-purple-500 bg-purple-50 cursor-pointer" 
                      : canSelect
                      ? "border-gray-200 hover:border-purple-300 hover:bg-purple-25 cursor-pointer"
                      : "border-gray-200 bg-gray-50 cursor-not-allowed opacity-60"
                  )}
                  onClick={() => {
                    if (isSelected || canSelect) {
                      handleSecondChoiceSelect(subject)
                    }
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-900">{subject}</h3>
                        <Badge className={getDifficultyColor(subjectInfo.difficulty)}>
                          {subjectInfo.difficulty === 'easy' ? '简单' : 
                           subjectInfo.difficulty === 'medium' ? '中等' : '困难'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        {subjectInfo.description}
                      </p>
                      <div>
                        <span className="text-xs font-medium text-gray-700">适合人群：</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {subjectInfo.suitableFor.slice(0, 2).map(trait => (
                            <Badge key={trait} variant="outline" className="text-xs">
                              {trait}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      {isSelected && (
                        <CheckCircle className="w-6 h-6 text-purple-500" />
                      )}
                      {!canSelect && !isSelected && (
                        <AlertCircle className="w-6 h-6 text-gray-400" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowInfo(showInfo === subject ? null : subject)
                        }}
                      >
                        <Info className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {showInfo === subject && (
                    <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                      <div className="space-y-2">
                        <div>
                          <span className="text-xs font-medium text-gray-700">学科特点：</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {subjectInfo.characteristics.map(char => (
                              <Badge key={char} variant="outline" className="text-xs">
                                {char}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-gray-700">相关专业：</span>
                          <p className="text-xs text-gray-600 mt-1">
                            {subjectInfo.relatedMajors.join('、')}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
          
          {selection.secondChoices.length === 2 && (
            <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 text-green-700">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">
                  已选择：{selection.secondChoices.join(' + ')}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

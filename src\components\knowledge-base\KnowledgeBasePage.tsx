import { useState } from "react"
import { ArrowLeft, BookOpen, TrendingUp, HelpCircle, Star, AlertTriangle, Trophy, ChevronRight, Search, Filter } from "lucide-react"
import { Button } from "../ui/button"

interface KnowledgeBasePageProps {
  onBack: () => void
  onNavigate?: (page: string) => void
}

export function KnowledgeBasePage({ onBack, onNavigate }: KnowledgeBasePageProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const knowledgeCategories = [
    {
      id: "industry-trends",
      title: "行业趋势",
      description: "了解各行业发展前景，把握未来就业方向",
      icon: TrendingUp,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      articles: 156,
      lastUpdated: "2024-01-15"
    },
    {
      id: "volunteer-qa",
      title: "志愿100问",
      description: "志愿填报常见问题解答，专家权威指导",
      icon: HelpCircle,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      articles: 100,
      lastUpdated: "2024-01-20"
    },
    {
      id: "popular-majors",
      title: "热门专业",
      description: "当前最受欢迎的专业详解，就业前景分析",
      icon: Star,
      color: "from-yellow-500 to-orange-500",
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      articles: 89,
      lastUpdated: "2024-01-18"
    },
    {
      id: "cautious-majors",
      title: "慎选专业",
      description: "需要谨慎考虑的专业，避免就业陷阱",
      icon: AlertTriangle,
      color: "from-red-500 to-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
      articles: 45,
      lastUpdated: "2024-01-12"
    },
    {
      id: "golden-jobs",
      title: "金饭碗",
      description: "高薪稳定职业推荐，铁饭碗专业指南",
      icon: Trophy,
      color: "from-amber-500 to-yellow-500",
      bgColor: "bg-amber-50",
      borderColor: "border-amber-200",
      articles: 67,
      lastUpdated: "2024-01-16"
    }
  ]

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId)
    if (onNavigate) {
      onNavigate(categoryId)
    }
  }

  const filteredCategories = knowledgeCategories.filter(category =>
    category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回首页</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <BookOpen className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-800">高考知识库</h1>
              </div>
            </div>
            
            {/* 搜索框 */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索知识库内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="container mx-auto px-4 py-8">
        {/* 页面标题和描述 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            高考知识库
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            汇聚专业的高考志愿填报知识，为您的升学之路提供全方位指导
          </p>
        </div>

        {/* 知识分类网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredCategories.map((category) => (
            <div
              key={category.id}
              className={`${category.bgColor} ${category.borderColor} border-2 rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 group`}
              onClick={() => handleCategoryClick(category.id)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 bg-gradient-to-r ${category.color} rounded-xl`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
              </div>
              
              <h3 className="text-xl font-bold text-gray-800 mb-2">
                {category.title}
              </h3>
              
              <p className="text-gray-600 mb-4 leading-relaxed">
                {category.description}
              </p>
              
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>{category.articles} 篇文章</span>
                <span>更新于 {category.lastUpdated}</span>
              </div>
            </div>
          ))}
        </div>

        {/* 搜索结果为空时的提示 */}
        {filteredCategories.length === 0 && searchQuery && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">未找到相关内容</h3>
            <p className="text-gray-600">请尝试其他关键词或浏览所有分类</p>
          </div>
        )}

        {/* 统计信息 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">
            知识库统计
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {knowledgeCategories.reduce((sum, cat) => sum + cat.articles, 0)}
              </div>
              <div className="text-sm text-gray-600">总文章数</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {knowledgeCategories.length}
              </div>
              <div className="text-sm text-gray-600">知识分类</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                98.5%
              </div>
              <div className="text-sm text-gray-600">内容准确率</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                24h
              </div>
              <div className="text-sm text-gray-600">更新频率</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import {
  GraduationCap,
  Clock,
  TrendingUp,
  MapPin,
  ChevronRight,
  Briefcase,
  DollarSign
} from 'lucide-react'

// 临时类型定义
interface Major {
  id: string
  code: string
  name: string
  englishName?: string
  category: string
  subCategory?: string
  degree: string
  duration: number
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
  // 新增字段
  graduateScale?: string
  recommendSchools?: string[]
  careerDirection?: string
  isRecommended?: boolean
}

interface MajorCardProps {
  major: Major
  onClick?: () => void
  className?: string
}

export function MajorCard({ major, onClick, className }: MajorCardProps) {
  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `¥${(salary / 10000).toFixed(1)}万`
    }
    return `¥${salary.toLocaleString()}`
  }

  const getTrendIcon = () => {
    if (!major.prospects) return null
    switch (major.prospects.trend) {
      case 'rising':
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'stable':
        return <div className="w-4 h-4 bg-blue-600 rounded-full" />
      case 'declining':
        return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />
      default:
        return null
    }
  }

  return (
    <Card
      className={cn(
        "p-4 hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-200 hover:border-blue-300",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-bold text-gray-900">{major.name}</h3>
            <span className="text-sm text-gray-500">({major.code})</span>
            {getTrendIcon()}
          </div>

          <div className="flex items-center gap-3 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{major.duration}年</span>
            </div>
            <div className="flex items-center gap-1">
              <GraduationCap className="w-4 h-4" />
              <span>{major.degree}学位</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{major.category}</span>
            </div>
          </div>
        </div>

        <Button variant="ghost" size="sm" className="flex-shrink-0">
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      {/* 就业信息 - 简化版 */}
      {(major.employment?.rate || major.employment?.avgSalary) && (
        <div className="flex gap-3 mb-3">
          {major.employment?.rate && (
            <div className="flex items-center gap-1 text-sm">
              <Briefcase className="w-4 h-4 text-green-600" />
              <span className="text-green-700 font-medium">
                就业率 {major.employment.rate.toFixed(1)}%
              </span>
            </div>
          )}

          {major.employment?.avgSalary && (
            <div className="flex items-center gap-1 text-sm">
              <DollarSign className="w-4 h-4 text-blue-600" />
              <span className="text-blue-700 font-medium">
                {formatSalary(major.employment.avgSalary)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* 专业特色 - 最多显示2个 */}
      {major.features && major.features.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {major.features.slice(0, 2).map((feature, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-orange-50 text-orange-700 rounded text-xs font-medium"
            >
              {feature}
            </span>
          ))}
          {major.features.length > 2 && (
            <span className="px-2 py-1 bg-gray-50 text-gray-600 rounded text-xs">
              +{major.features.length - 2}
            </span>
          )}
        </div>
      )}
    </Card>
  )
}

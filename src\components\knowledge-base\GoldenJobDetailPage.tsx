import { useState, useEffect } from "react"
import { ArrowLeft, Briefcase, Calendar, Loader2, AlertCircle, BookOpen, MapPin, GraduationCap, Building } from "lucide-react"
import { Button } from "../ui/button"
import { 
  getJobDetail, 
  getProvinceList,
  formatTimestamp, 
  getCompanyTypeInfo,
  parseSchools,
  type GoldenJobItem,
  type JobDetailItem,
  type ProvinceItem
} from "../../services/goldenJobsApi"

interface GoldenJobDetailPageProps {
  onBack: () => void
  selectedJob: GoldenJobItem | null
}

export function GoldenJobDetailPage({ onBack, selectedJob }: GoldenJobDetailPageProps) {
  const [detailData, setDetailData] = useState<JobDetailItem | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [provinces, setProvinces] = useState<ProvinceItem[]>([])
  const [selectedProvince, setSelectedProvince] = useState('')
  const [selectedSchool, setSelectedSchool] = useState('')
  const [schools, setSchools] = useState<string[]>([])

  // 加载省份列表
  const loadProvinces = async () => {
    try {
      const response = await getProvinceList()
      setProvinces(response.data.list.sort((a, b) => a.sort - b.sort))
    } catch (err) {
      console.error('加载省份列表失败:', err)
    }
  }

  // 加载详情数据
  const loadDetailData = async (dirName: string, schoolName: string, provinceName: string = '') => {
    setLoading(true)
    setError(null)

    try {
      const response = await getJobDetail(dirName, schoolName, provinceName)
      if (response.data.list.length > 0) {
        setDetailData(response.data.list[0])
      } else {
        setError('未找到详细内容')
      }
    } catch (err) {
      console.error('加载详情失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    if (selectedJob) {
      const schoolList = parseSchools(selectedJob.schools)
      setSchools(schoolList)
      if (schoolList.length > 0) {
        setSelectedSchool(schoolList[0])
      }
      loadProvinces()
    }
  }, [selectedJob])

  // 当选择改变时重新加载数据
  useEffect(() => {
    if (selectedJob && selectedSchool) {
      const provinceName = provinces.find(p => p.value === selectedProvince)?.label || ''
      loadDetailData(selectedJob.name, selectedSchool, provinceName)
    }
  }, [selectedJob, selectedSchool, selectedProvince, provinces])

  if (!selectedJob) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-white to-yellow-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">未选择企业</h3>
          <p className="text-gray-600 mb-4">请先选择一个企业查看详情</p>
          <Button onClick={onBack}>返回列表</Button>
        </div>
      </div>
    )
  }

  const companyInfo = getCompanyTypeInfo(selectedJob.name)

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-white to-yellow-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回列表</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <Briefcase className="w-6 h-6 text-yellow-600" />
                <h1 className="text-xl font-bold text-gray-800">金饭碗详情</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 企业标题卡片 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <span className={`px-3 py-1 text-sm font-medium rounded-full ${companyInfo.bgColor} ${companyInfo.color} flex items-center space-x-1`}>
                  <span>{companyInfo.icon}</span>
                  <span>{companyInfo.type}</span>
                </span>
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <Calendar className="w-4 h-4" />
                  <span>{formatTimestamp(selectedJob.createTime)}</span>
                </div>
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-4">
                {selectedJob.name}
              </h1>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <GraduationCap className="w-4 h-4" />
                  <span>合作院校: {schools.length}所</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Building className="w-4 h-4" />
                  <span>国有企业</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 选择器 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">查看详情</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 学校选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择院校
              </label>
              <select
                value={selectedSchool}
                onChange={(e) => setSelectedSchool(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
              >
                {schools.map((school, index) => (
                  <option key={index} value={school}>
                    {school}
                  </option>
                ))}
              </select>
            </div>

            {/* 省份选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                选择省份 (可选)
              </label>
              <select
                value={selectedProvince}
                onChange={(e) => setSelectedProvince(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
              >
                <option value="">全国通用</option>
                {provinces.map((province) => (
                  <option key={province.id} value={province.value}>
                    {province.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 详细内容 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          <div className="flex items-center space-x-2 mb-6">
            <BookOpen className="w-5 h-5 text-yellow-600" />
            <h2 className="text-xl font-semibold text-gray-800">
              {selectedSchool} - {selectedJob.name} 就业指南
            </h2>
            {selectedProvince && (
              <span className="flex items-center space-x-1 text-sm text-gray-500">
                <MapPin className="w-3 h-3" />
                <span>{provinces.find(p => p.value === selectedProvince)?.label}</span>
              </span>
            )}
          </div>
          
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-yellow-600 mr-3" />
              <span className="text-gray-600">加载详细内容中...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button 
                onClick={() => {
                  if (selectedJob && selectedSchool) {
                    const provinceName = provinces.find(p => p.value === selectedProvince)?.label || ''
                    loadDetailData(selectedJob.name, selectedSchool, provinceName)
                  }
                }}
                variant="outline"
              >
                重试
              </Button>
            </div>
          ) : detailData ? (
            <div className="prose prose-lg max-w-none">
              <div 
                className="text-gray-800 leading-relaxed [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h2]:text-xl [&>h2]:font-semibold [&>h2]:mb-3 [&>h3]:text-lg [&>h3]:font-medium [&>h3]:mb-2 [&>h4]:text-base [&>h4]:font-medium [&>h4]:mb-2 [&>p]:mb-4 [&>ul]:mb-4 [&>ul]:pl-6 [&>ol]:mb-4 [&>ol]:pl-6 [&>li]:mb-2 [&>li]:list-disc [&>ol>li]:list-decimal [&>hr]:my-6 [&>hr]:border-gray-300 [&>strong]:font-semibold [&>em]:italic [&>table]:w-full [&>table]:border-collapse [&>table]:border [&>table]:border-gray-300 [&>table]:mb-4 [&>th]:border [&>th]:border-gray-300 [&>th]:bg-gray-100 [&>th]:p-2 [&>th]:text-left [&>td]:border [&>td]:border-gray-300 [&>td]:p-2 [&>a]:text-blue-600 [&>a]:underline [&>a:hover]:text-blue-800"
                dangerouslySetInnerHTML={{ __html: detailData.content }}
              />
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">暂无详细内容</h3>
              <p className="text-gray-600">该企业的详细就业指南正在完善中</p>
            </div>
          )}
        </div>

        {/* 相关推荐 */}
        <div className="mt-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Briefcase className="w-5 h-5 mr-2 text-yellow-600" />
            就业建议
          </h3>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="text-gray-700">
              <p className="mb-2">• <strong>提前准备</strong>：关注企业校招时间，提前准备相关专业知识和技能</p>
              <p className="mb-2">• <strong>院校选择</strong>：优先选择与目标企业有合作关系的院校</p>
              <p className="mb-2">• <strong>专业匹配</strong>：选择与企业需求高度匹配的专业方向</p>
              <p>• <strong>实习经验</strong>：争取在相关企业或行业获得实习经验</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

import React, { useState, useEffect } from 'react'
import { getCollegeScoreLine } from '../../services/scoreLineApiSimple'
import type { CollegeScoreLineData } from '../../services/scoreLineApiSimple'

interface TestResult {
  university: string
  scoreLines: CollegeScoreLineData[]
  error?: string
}

export function GoalSettingTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [loading, setLoading] = useState(false)
  const [userScore, setUserScore] = useState(550)

  // 测试省份和大学
  const testProvinces = ['安徽', '江苏', '浙江', '北京', '上海']
  const testUniversities = [
    '清华大学',
    '北京大学',
    '复旦大学',
    '上海交通大学',
    '中南大学'
  ]

  const runTest = async () => {
    setLoading(true)
    setTestResults([])

    const results: TestResult[] = []

    // 首先测试最简单的按省份查询
    console.log('🔍 测试最简单的按省份查询...')
    try {
      const response = await getCollegeScoreLine({
        searchtype: 'PROVINCENAME',
        keyword: '安徽',
        pageindex: 1,
        pagesize: 5
      })

      console.log('📊 API响应:', response)

      if (response.DataStatus?.StatusCode === 100) {
        const provinceData = response.Data || []
        console.log(`✅ 安徽省数据获取成功，获得 ${provinceData.length} 条记录`)

        results.push({
          university: '安徽省所有大学（最简单查询）',
          scoreLines: provinceData,
          error: undefined
        })
      } else {
        results.push({
          university: '安徽省所有大学（最简单查询）',
          scoreLines: [],
          error: `API返回错误: ${response.DataStatus?.StatusDescription || '未知错误'}`
        })
      }
    } catch (error) {
      console.log('❌ 按省份查询失败:', error)
      results.push({
        university: '安徽省所有大学（最简单查询）',
        scoreLines: [],
        error: error instanceof Error ? error.message : '未知错误'
      })
    }

    // 如果第一个测试成功，再测试带更多参数的查询
    if (results[0] && !results[0].error) {
      console.log('🔍 测试带更多参数的按省份查询...')
      try {
        const response = await getCollegeScoreLine({
          searchtype: 'PROVINCENAME',
          keyword: '安徽',
          pageindex: 1,
          pagesize: 10,
          year: 2024,
          type: '物理类'
        })

        if (response.DataStatus?.StatusCode === 100) {
          const provinceData = response.Data || []
          console.log(`✅ 安徽省详细数据获取成功，获得 ${provinceData.length} 条记录`)

          results.push({
            university: '安徽省所有大学（带参数查询）',
            scoreLines: provinceData.slice(0, 5),
            error: undefined
          })
        } else {
          results.push({
            university: '安徽省所有大学（带参数查询）',
            scoreLines: [],
            error: `API返回错误: ${response.DataStatus?.StatusDescription || '未知错误'}`
          })
        }
      } catch (error) {
        console.log('❌ 带参数查询失败:', error)
        results.push({
          university: '安徽省所有大学（带参数查询）',
          scoreLines: [],
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    // 然后测试一个简单的大学名称查询
    if (results.length > 0 && !results[results.length - 1].error) {
      console.log('🔍 测试按大学名称查询...')
      try {
        const response = await getCollegeScoreLine({
          searchtype: 'COLLEGENAME',
          keyword: '清华大学',
          pageindex: 1,
          pagesize: 3
        })

        console.log('📊 大学查询API响应:', response)

        if (response.DataStatus?.StatusCode === 100) {
          results.push({
            university: '清华大学（按名称查询）',
            scoreLines: response.Data || []
          })
          console.log(`✅ 清华大学数据获取成功，获得 ${response.Data?.length || 0} 条记录`)
        } else {
          results.push({
            university: '清华大学（按名称查询）',
            scoreLines: [],
            error: `API返回错误: ${response.DataStatus?.StatusDescription || '未知错误'}`
          })
        }
      } catch (error) {
        results.push({
          university: '清华大学（按名称查询）',
          scoreLines: [],
          error: error instanceof Error ? error.message : '未知错误'
        })
        console.log(`❌ 清华大学查询失败:`, error)
      }
    }

    setTestResults(results)
    setLoading(false)
  }

  // 计算录取可能性
  const calculateAdmissionPossibility = (scoreLines: CollegeScoreLineData[]) => {
    if (scoreLines.length === 0) return { label: '暂无数据', color: 'text-gray-500' }

    const latestScoreLine = scoreLines[0]
    const minScore = parseInt(latestScoreLine.LowestScore) || 0
    const avgScore = parseInt(latestScoreLine.AverageScore) || 0

    if (userScore >= avgScore + 20) {
      return { label: '录取希望大', color: 'text-green-600' }
    } else if (userScore >= minScore) {
      return { label: '有录取希望', color: 'text-yellow-600' }
    } else if (userScore >= minScore - 20) {
      return { label: '录取希望较小', color: 'text-orange-600' }
    } else {
      return { label: '录取困难', color: 'text-red-600' }
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">目标设置功能测试</h1>
        
        {/* 用户分数设置 */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            模拟用户分数：
          </label>
          <input
            type="number"
            value={userScore}
            onChange={(e) => setUserScore(Number(e.target.value))}
            className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <span className="ml-2 text-sm text-gray-600">分</span>
        </div>

        {/* 测试按钮 */}
        <button
          onClick={runTest}
          disabled={loading}
          className="mb-6 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? '测试中...' : '开始测试录取分数线API'}
        </button>

        {/* 测试结果 */}
        {testResults.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">测试结果</h2>
            
            {testResults.map((result, index) => {
              const admission = calculateAdmissionPossibility(result.scoreLines)
              
              return (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900">
                      {result.university}
                    </h3>
                    <span className={`text-sm font-medium ${admission.color}`}>
                      {admission.label}
                    </span>
                  </div>

                  {result.error ? (
                    <div className="text-red-600 text-sm">
                      错误: {result.error}
                    </div>
                  ) : result.scoreLines.length > 0 ? (
                    <div className="space-y-2">
                      {result.scoreLines.map((scoreLine, idx) => (
                        <div key={idx} className="bg-gray-50 p-3 rounded text-sm">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <span className="font-medium">年份:</span> {scoreLine.Year}
                            </div>
                            <div>
                              <span className="font-medium">最低分:</span> {scoreLine.LowestScore}
                            </div>
                            <div>
                              <span className="font-medium">平均分:</span> {scoreLine.AverageScore}
                            </div>
                            <div>
                              <span className="font-medium">最高分:</span> {scoreLine.HighestScore}
                            </div>
                            <div>
                              <span className="font-medium">省份:</span> {scoreLine.Province}
                            </div>
                            <div>
                              <span className="font-medium">批次:</span> {scoreLine.AdmissionBatch}
                            </div>
                            <div>
                              <span className="font-medium">科目:</span> {scoreLine.TypeName}
                            </div>
                            {scoreLine.LowestRank && (
                              <div>
                                <span className="font-medium">位次:</span> {scoreLine.LowestRank}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm">
                      暂无录取分数线数据
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}

        {/* 功能说明 */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">功能说明</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• <strong>按省份查询</strong>：获取指定省份所有大学的录取分数线（用于获取可选择的大学列表）</li>
            <li>• <strong>按大学名称查询</strong>：获取特定大学在指定省份的录取分数线</li>
            <li>• <strong>录取可能性计算</strong>：根据用户分数与录取分数线对比，评估录取可能性</li>
            <li>• <strong>智能排序</strong>：按录取可能性对大学进行排序，优先显示合适的选择</li>
            <li>• <strong>数据来源</strong>：咕咕数据API，包含2015-2025年全国高校录取数据</li>
          </ul>

          <div className="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
            <h4 className="font-medium text-blue-900 mb-1">API使用方式</h4>
            <p className="text-sm text-blue-800">
              • searchtype=PROVINCENAME：按省份获取所有大学（用于构建可选择的大学列表）<br/>
              • searchtype=COLLEGENAME：按大学名称获取特定大学的录取分数线
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// 导入类型
import type { University } from '../types/university'

// 注意：此文件包含旧的mock数据结构，现在使用真实API数据
// 保留此文件仅用于向后兼容，实际使用中请使用 universityApi.ts

// 空的mock数据数组，因为现在使用真实API
export const mockUniversities: University[] = []

// 保留的常量定义供其他组件使用
export const provinces = [
  '北京', '上海', '天津', '重庆', '河北', '山西', '辽宁', '吉林', '黑龙江',
  '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南',
  '广东', '广西', '海南', '四川', '贵州', '云南', '西藏', '陕西', '甘肃',
  '青海', '宁夏', '新疆', '内蒙古', '台湾', '香港', '澳门'
] as const

export const universityTypes = ['985', '211', '双一流', '普通本科', '专科'] as const

export const universityCategories = [
  '综合', '理工', '师范', '农林', '医药', '财经', '政法', '艺术', '体育', '民族', '军事', '其他'
] as const

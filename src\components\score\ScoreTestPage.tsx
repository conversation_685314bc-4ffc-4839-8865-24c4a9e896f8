import { useState } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { getProvinceScoreLine, getCollegeScoreLine } from '../../services/scoreLineApiSimple'

export function ScoreTestPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const testProvinceAPI = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      console.log('🧪 测试省录取分数线API...')
      const response = await getProvinceScoreLine({
        keyword: '安徽',
        year: 2024,
        category: '物理类'
      })
      setResult(response)
      console.log('✅ 省录取分数线API测试成功:', response)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '测试失败'
      setError(errorMessage)
      console.error('❌ 省录取分数线API测试失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const testCollegeAPI = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    
    try {
      console.log('🧪 测试高校录取分数线API...')
      const response = await getCollegeScoreLine({
        searchtype: 'PROVINCENAME',
        keyword: '安徽',
        pageindex: 1,
        pagesize: 5,
        year: 2024,
        type: '物理类'
      })
      setResult(response)
      console.log('✅ 高校录取分数线API测试成功:', response)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '测试失败'
      setError(errorMessage)
      console.error('❌ 高校录取分数线API测试失败:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">录取分数线API测试</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Button
          onClick={testProvinceAPI}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600"
        >
          {loading ? '测试中...' : '测试省录取分数线API'}
        </Button>
        
        <Button
          onClick={testCollegeAPI}
          disabled={loading}
          className="bg-green-500 hover:bg-green-600"
        >
          {loading ? '测试中...' : '测试高校录取分数线API'}
        </Button>
      </div>

      {error && (
        <Card className="p-4 mb-6 border-red-200 bg-red-50">
          <div className="text-red-800">
            <strong>错误:</strong> {error}
          </div>
        </Card>
      )}

      {result && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">API响应结果</h2>
          
          <div className="mb-4">
            <strong>状态码:</strong> {result.DataStatus?.StatusCode}
          </div>
          
          <div className="mb-4">
            <strong>状态描述:</strong> {result.DataStatus?.StatusDescription}
          </div>
          
          <div className="mb-4">
            <strong>数据条数:</strong> {result.Data?.length || 0}
          </div>
          
          <div className="mb-4">
            <strong>总数据量:</strong> {result.DataStatus?.DataTotalCount || 0}
          </div>

          {result.Data && result.Data.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">示例数据:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result.Data[0], null, 2)}
              </pre>
            </div>
          )}
        </Card>
      )}
    </div>
  )
}

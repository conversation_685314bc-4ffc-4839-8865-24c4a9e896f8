/**
 * 简化版录取分数线API服务
 */

const API_BASE_URL = 'http://localhost:3001/api/gugudata'
const API_KEY = 'E6X2G4QC2VC2LA3WN3K46KLCVAZKEXLM'

// 省录取分数线查询参数
export interface ProvinceScoreLineFilters {
  keyword?: string
  year?: number
  category?: string
}

// 省录取分数线数据结构
export interface ProvinceScoreLineData {
  Province: string
  Category: string
  ScoreBatch: string
  Year: number
  Score: number
  SpecialName: string | null
}

// 高校录取分数线查询参数
export interface CollegeScoreLineFilters {
  searchtype: 'PROVINCENAME' | 'COLLEGENAME'
  keyword: string
  pageindex?: number
  pagesize?: number
  year?: number
  min?: number
  type?: string
  keywordstrict?: boolean
  enrollprovince?: string
  batchname?: string
  collegeprovincename?: string
  enrollmenttype?: string
  schooluuid?: string
  sort?: string
  minrange?: string
}

// 高校录取分数线数据结构
export interface CollegeScoreLineData {
  Province: string
  SchoolUUID: string
  CollegeName: string
  Year: number
  HighestScore: string
  AverageScore: string
  LowestScore: string
  LowestRank: string
  ProvincialControlLine: string
  EnrollmentType: string
  SelectionLevel: string
  AdmissionBatch: string
  TypeName: string
  CourseSelection: string
  CourseSelectionName: string
  SchoolType: string
  SchoolInCity: string
  Is985: boolean
  Is211: boolean
  IsDualClass: boolean
  CoverImage: string
}

// API响应结构
export interface ApiResponse<T> {
  DataStatus: {
    StatusCode: number
    StatusDescription: string
    ResponseDateTime: string
    DataTotalCount: number
  }
  Data: T[]
}

// 常用的省份列表
export const PROVINCES = [
  '上海', '云南', '内蒙古', '北京', '台湾', '吉林', '四川', '天津', 
  '宁夏', '安徽', '山东', '山西', '广东', '广西', '新疆', '江苏', 
  '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '澳门', 
  '甘肃', '福建', '西藏', '贵州', '辽宁', '重庆', '陕西', '青海', 
  '香港', '黑龙江'
]

// 常用的科目类型（注意：建议使用 getAvailableSubjectSelections 函数获取动态数据）
export const SUBJECT_TYPES = [
  '理科', '文科', '综合', '艺术类', '物理类', '历史类', '体育类',
  '艺术文', '艺术理', '体育理', '体育文'
]

// 推荐使用的动态获取科目类型的函数
export { getAvailableSubjectSelections } from './oneScoreOneSectionApi'

// 常用的录取批次
export const ADMISSION_BATCHES = [
  '本科一批', '本科二批', '本科批', '专科批', '本科三批',
  '艺术类（本科）', '艺术类（高职专科）', '体育类（本科）',
  '体育类（高职专科）', '特殊类型招生控制线'
]

/**
 * 获取省录取分数线数据
 */
export async function getProvinceScoreLine(filters: ProvinceScoreLineFilters = {}): Promise<ApiResponse<ProvinceScoreLineData>> {
  try {
    const params = new URLSearchParams()
    params.append('appkey', API_KEY)
    
    if (filters.keyword !== undefined) {
      params.append('keyword', filters.keyword)
    } else {
      params.append('keyword', '')
    }
    
    if (filters.year) {
      params.append('year', filters.year.toString())
    } else {
      params.append('year', '')
    }
    
    if (filters.category) {
      params.append('category', filters.category)
    } else {
      params.append('category', '')
    }
    
    const url = `${API_BASE_URL}/metadata/ceeprovince?${params.toString()}`
    
    console.log('🔍 请求省录取分数线:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 省录取分数线获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 省录取分数线获取失败:', error)
    throw error
  }
}

/**
 * 获取高校录取分数线数据
 */
export async function getCollegeScoreLine(filters: CollegeScoreLineFilters): Promise<ApiResponse<CollegeScoreLineData>> {
  try {
    console.log('🔍 开始请求高校录取分数线，参数:', filters)

    // 构建查询参数，只添加有值的参数
    const params = new URLSearchParams()
    params.append('appkey', API_KEY)
    params.append('searchtype', filters.searchtype)
    params.append('keyword', filters.keyword)
    params.append('pageindex', (filters.pageindex || 1).toString())
    params.append('pagesize', (filters.pagesize || 10).toString())

    // 只有当参数有值时才添加
    if (filters.year !== undefined && filters.year !== null) {
      params.append('year', filters.year.toString())
    }

    if (filters.min !== undefined && filters.min !== null) {
      params.append('min', filters.min.toString())
    }

    if (filters.type) {
      params.append('type', filters.type)
    }

    if (filters.keywordstrict !== undefined) {
      params.append('keywordstrict', filters.keywordstrict.toString())
    }

    if (filters.enrollprovince) {
      params.append('enrollprovince', filters.enrollprovince)
    }

    if (filters.batchname) {
      params.append('batchname', filters.batchname)
    }

    if (filters.collegeprovincename) {
      params.append('collegeprovincename', filters.collegeprovincename)
    }

    if (filters.enrollmenttype) {
      params.append('enrollmenttype', filters.enrollmenttype)
    }

    if (filters.schooluuid) {
      params.append('schooluuid', filters.schooluuid)
    }

    if (filters.sort) {
      params.append('sort', filters.sort)
    }

    if (filters.minrange) {
      params.append('minrange', filters.minrange)
    }

    const url = `${API_BASE_URL}/metadata/ceecollegeline?${params.toString()}`

    console.log('🔍 请求URL:', url)
    console.log('📋 请求参数:', Object.fromEntries(params))

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors',
    })

    console.log('📡 响应状态:', response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ API响应错误:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      })
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`)
    }

    const data = await response.json()
    console.log('✅ 高校录取分数线获取成功:', {
      statusCode: data.DataStatus?.StatusCode,
      dataCount: data.Data?.length || 0,
      description: data.DataStatus?.StatusDescription
    })
    return data
  } catch (error) {
    console.error('❌ 高校录取分数线获取失败:', error)
    throw error
  }
}

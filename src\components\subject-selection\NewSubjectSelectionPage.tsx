import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { 
  ArrowLeft, 
  BookOpenCheck, 
  Target,
  Lightbulb,
  Brain,
  User,
  School,
  GraduationCap,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Info,
  Sparkles,
  Search,
  Filter
} from 'lucide-react'

// 导入新组件
import { IntelligentRecommendationMode } from './IntelligentRecommendationMode'
import { ManualSelectionMode } from './ManualSelectionMode'

// 导入类型和数据
import type { UserSelection } from './types'
import { subjectData, popularCombinations, majorRequirements } from './data'

interface NewSubjectSelectionPageProps {
  onBack?: () => void
  className?: string
}

// 选科模式类型
type SelectionMode = 'intelligent' | 'manual'

// 智能推荐的输入类型
type RecommendationType = 'university' | 'major'

// 智能推荐状态
interface IntelligentRecommendation {
  type: RecommendationType
  target: string // 目标学校或专业名称
  isAnalyzing: boolean
  recommendations: SubjectCombinationRecommendation[]
}

// 推荐的选科组合
interface SubjectCombinationRecommendation {
  combination: {
    firstChoice: '物理' | '历史'
    secondChoices: string[]
    name: string
  }
  matchScore: number // 匹配度分数 0-100
  reasons: string[] // 推荐理由
  advantages: string[] // 优势
  risks: string[] // 风险提示
  majorCoverage: number // 专业覆盖率
  competitiveness: 'low' | 'medium' | 'high' // 竞争激烈程度
}

export function NewSubjectSelectionPage({ onBack, className }: NewSubjectSelectionPageProps) {
  // 选科模式状态
  const [selectionMode, setSelectionMode] = useState<SelectionMode>('intelligent')
  
  // 智能推荐状态
  const [intelligentRec, setIntelligentRec] = useState<IntelligentRecommendation>({
    type: 'university',
    selectedUniversities: [],
    selectedMajors: [],
    isAnalyzing: false,
    recommendations: []
  })
  
  // 手动选科状态
  const [userSelection, setUserSelection] = useState<UserSelection>({
    firstChoice: null,
    secondChoices: [],
    isComplete: false
  })

  // 当前活跃的标签页（仅在手动模式下使用）
  const [activeTab, setActiveTab] = useState<'selector' | 'analysis' | 'majors' | 'guide'>('selector')

  // 模式切换处理
  const handleModeSwitch = (mode: SelectionMode) => {
    setSelectionMode(mode)
    // 重置状态
    if (mode === 'intelligent') {
      setIntelligentRec({
        type: 'university',
        selectedUniversities: [],
        selectedMajors: [],
        isAnalyzing: false,
        recommendations: []
      })
    } else {
      setUserSelection({
        firstChoice: null,
        secondChoices: [],
        isComplete: false
      })
      setActiveTab('selector')
    }
  }

  // 智能分析处理
  const handleIntelligentAnalysis = async () => {
    const hasSelections = intelligentRec.type === 'university'
      ? intelligentRec.selectedUniversities.length > 0
      : intelligentRec.selectedMajors.length > 0

    if (!hasSelections) {
      alert(`请先选择目标${intelligentRec.type === 'university' ? '学校' : '专业'}`)
      return
    }

    setIntelligentRec(prev => ({ ...prev, isAnalyzing: true }))

    // 模拟分析过程
    setTimeout(() => {
      const mockRecommendations = generateMockRecommendations(intelligentRec.type)
      setIntelligentRec(prev => ({
        ...prev,
        isAnalyzing: false,
        recommendations: mockRecommendations
      }))
    }, 2000)
  }

  // 生成模拟推荐数据
  const generateMockRecommendations = (type: RecommendationType): SubjectCombinationRecommendation[] => {
    // 这里应该是真实的智能分析算法
    // 现在使用模拟数据
    if (type === 'university') {
      return generateUniversityBasedRecommendations(intelligentRec.selectedUniversities.map(u => u.CollegeName))
    } else {
      return generateMajorBasedRecommendations(intelligentRec.selectedMajors.map(m => m.name))
    }
  }

  // 基于目标学校生成推荐
  const generateUniversityBasedRecommendations = (universities: string[]): SubjectCombinationRecommendation[] => {
    if (universities.length === 0) return []

    // 模拟不同学校的推荐逻辑
    const isTopUniversity = universities.some(university =>
      ['清华', '北大', '复旦', '交大', '浙江', '中科大'].some(name => university.includes(name))
    )
    const isEngineeringFocused = universities.some(university =>
      ['清华', '交大', '华科', '西交', '哈工大', '理工'].some(name => university.includes(name))
    )
    
    const recommendations: SubjectCombinationRecommendation[] = []
    
    if (isTopUniversity && isEngineeringFocused) {
      recommendations.push({
        combination: {
          firstChoice: '物理',
          secondChoices: ['化学', '生物'],
          name: '物理+化学+生物'
        },
        matchScore: 95,
        reasons: [
          '选中的院校工科专业实力强劲，物化生组合专业覆盖最全',
          '该组合是传统理科强势组合，符合顶尖工科院校要求',
          '有利于报考这些学校热门的工程、医学、理学等专业'
        ],
        advantages: ['专业选择最多', '理科基础扎实', '就业前景广阔'],
        risks: ['竞争激烈', '学习难度大', '需要较强的理科基础'],
        majorCoverage: 96.4,
        competitiveness: 'high'
      })
    }
    
    // 添加更多推荐...
    recommendations.push({
      combination: {
        firstChoice: '物理',
        secondChoices: ['化学', '地理'],
        name: '物理+化学+地理'
      },
      matchScore: 88,
      reasons: [
        '保持理科优势的同时降低学习难度',
        '地理学科相对简单，有助于提高总分',
        '适合冲刺选中院校的理工科专业'
      ],
      advantages: ['文理兼顾', '学习压力适中', '专业覆盖广'],
      risks: ['思维跨度较大', '部分顶尖工科专业可能受限'],
      majorCoverage: 95.8,
      competitiveness: 'medium'
    })

    return recommendations.sort((a, b) => b.matchScore - a.matchScore)
  }

  // 基于目标专业生成推荐
  const generateMajorBasedRecommendations = (majors: string[]): SubjectCombinationRecommendation[] => {
    if (majors.length === 0) return []

    const recommendations: SubjectCombinationRecommendation[] = []

    // 根据专业类型生成不同推荐
    const hasComputerMajor = majors.some(major =>
      major.includes('计算机') || major.includes('软件') || major.includes('人工智能')
    )
    const hasMedicalMajor = majors.some(major =>
      major.includes('医学') || major.includes('临床') || major.includes('护理')
    )

    if (hasComputerMajor) {
      recommendations.push({
        combination: {
          firstChoice: '物理',
          secondChoices: ['化学', '生物'],
          name: '物理+化学+生物'
        },
        matchScore: 92,
        reasons: [
          '计算机专业通常要求物理作为必选科目',
          '化学和生物有助于理解算法和数据结构的底层逻辑',
          '该组合为计算机相关专业提供最佳基础'
        ],
        advantages: ['专业匹配度高', '基础扎实', '发展潜力大'],
        risks: ['竞争激烈', '学习难度较大'],
        majorCoverage: 96.4,
        competitiveness: 'high'
      })
    }

    if (hasMedicalMajor) {
      recommendations.push({
        combination: {
          firstChoice: '物理',
          secondChoices: ['化学', '生物'],
          name: '物理+化学+生物'
        },
        matchScore: 98,
        reasons: [
          '医学专业必须选择物理、化学、生物三科',
          '这是医学专业的标准配置，无其他选择',
          '为医学学习提供必要的科学基础'
        ],
        advantages: ['专业要求匹配', '科学基础扎实', '逻辑思维强'],
        risks: ['学习难度大', '竞争非常激烈'],
        majorCoverage: 96.4,
        competitiveness: 'high'
      })
    }
    
    return recommendations.sort((a, b) => b.matchScore - a.matchScore)
  }

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50", className)}>
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回</span>
              </Button>
              <div className="flex items-center space-x-2">
                <BookOpenCheck className="w-6 h-6 text-orange-500" />
                <h1 className="text-xl font-bold text-gray-900">高考选科</h1>
              </div>
            </div>
            
            {/* 模式切换按钮 */}
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <Button
                variant={selectionMode === 'intelligent' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleModeSwitch('intelligent')}
                className={cn(
                  "flex items-center space-x-2",
                  selectionMode === 'intelligent' 
                    ? "bg-orange-500 text-white" 
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                <Brain className="w-4 h-4" />
                <span>智能推荐</span>
              </Button>
              <Button
                variant={selectionMode === 'manual' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleModeSwitch('manual')}
                className={cn(
                  "flex items-center space-x-2",
                  selectionMode === 'manual' 
                    ? "bg-orange-500 text-white" 
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                <User className="w-4 h-4" />
                <span>自主选科</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 模式说明卡片 */}
        <Card className="mb-8 bg-gradient-to-r from-orange-50 to-blue-50 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                {selectionMode === 'intelligent' ? (
                  <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                ) : (
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {selectionMode === 'intelligent' ? '🤖 智能推荐模式' : '🎯 自主选科模式'}
                </h3>
                <p className="text-gray-700 mb-3">
                  {selectionMode === 'intelligent' 
                    ? '输入您的目标学校或专业，AI将为您分析并推荐最适合的选科组合，帮您科学决策。'
                    : '根据个人兴趣和优势科目，自主选择首选科目和再选科目，获得详细的组合分析。'
                  }
                </p>
                <div className="flex flex-wrap gap-2">
                  {selectionMode === 'intelligent' ? (
                    <>
                      <Badge className="bg-orange-100 text-orange-700">AI智能分析</Badge>
                      <Badge className="bg-orange-100 text-orange-700">目标导向</Badge>
                      <Badge className="bg-orange-100 text-orange-700">科学推荐</Badge>
                    </>
                  ) : (
                    <>
                      <Badge className="bg-blue-100 text-blue-700">个性化选择</Badge>
                      <Badge className="bg-blue-100 text-blue-700">详细分析</Badge>
                      <Badge className="bg-blue-100 text-blue-700">专业指导</Badge>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 内容区域 */}
        {selectionMode === 'intelligent' ? (
          <IntelligentRecommendationMode
            recommendation={intelligentRec}
            onRecommendationChange={setIntelligentRec}
            onAnalyze={handleIntelligentAnalysis}
          />
        ) : (
          <ManualSelectionMode
            userSelection={userSelection}
            onSelectionChange={setUserSelection}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
        )}
      </div>
    </div>
  )
}

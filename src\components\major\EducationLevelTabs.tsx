import { cn } from '../../lib/utils'

// 教育层次类型
type EducationLevel = '本科(普通)' | '本科(职业)' | '专科(高职)'

interface EducationLevelTabsProps {
  selectedLevel: EducationLevel
  onLevelChange: (level: EducationLevel) => void
  className?: string
}

export function EducationLevelTabs({ 
  selectedLevel, 
  onLevelChange, 
  className 
}: EducationLevelTabsProps) {
  const levels: EducationLevel[] = [
    '本科(普通)',
    '本科(职业)', 
    '专科(高职)'
  ]

  return (
    <div className={cn("flex bg-gray-100 rounded-lg p-1", className)}>
      {levels.map((level) => (
        <button
          key={level}
          onClick={() => onLevelChange(level)}
          className={cn(
            "flex-1 px-4 py-2 text-sm font-medium rounded-md transition-all duration-200",
            selectedLevel === level
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
          )}
        >
          {level}
        </button>
      ))}
    </div>
  )
}

import { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { goalService } from '../../services/goalService'
import type { ExamScore } from '../../types/goal'
import {
  Plus,
  Calendar,
  BookOpen,
  Save,
  ArrowLeft,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

interface ScoreEntryPageProps {
  onBack?: () => void
  onSave?: () => void
}

export function ScoreEntryPage({ onBack, onSave }: ScoreEntryPageProps) {
  const { user, isAuthenticated } = useAuth()
  const [formData, setFormData] = useState({
    examName: '',
    date: new Date().toISOString().split('T')[0],
    totalScore: '',
    subjects: {
      '语文': '',
      '数学': '',
      '英语': '',
      '物理': '',
      '化学': '',
      '生物': '',
      '政治': '',
      '历史': '',
      '地理': ''
    },
    ranking: '',
    classRanking: '',
    notes: ''
  })
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubjectChange = (subject: string, score: string) => {
    setFormData(prev => ({
      ...prev,
      subjects: {
        ...prev.subjects,
        [subject]: score
      }
    }))
  }

  const calculateTotalScore = () => {
    const subjectScores = Object.values(formData.subjects)
      .filter(score => score && !isNaN(Number(score)))
      .map(score => Number(score))
    
    if (subjectScores.length > 0) {
      const total = subjectScores.reduce((sum, score) => sum + score, 0)
      setFormData(prev => ({
        ...prev,
        totalScore: total.toString()
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || !isAuthenticated) {
      setError('请先登录')
      return
    }

    if (!formData.examName || !formData.totalScore) {
      setError('请填写考试名称和总分')
      return
    }

    try {
      setSaving(true)
      setError(null)

      // 过滤掉空的科目分数
      const subjects: { [key: string]: number } = {}
      Object.entries(formData.subjects).forEach(([subject, score]) => {
        if (score && !isNaN(Number(score))) {
          subjects[subject] = Number(score)
        }
      })

      const examScore: Omit<ExamScore, 'id' | 'createdAt' | 'updatedAt'> = {
        date: formData.date,
        examName: formData.examName,
        totalScore: Number(formData.totalScore),
        subjects,
        ranking: formData.ranking ? Number(formData.ranking) : undefined,
        classRanking: formData.classRanking ? Number(formData.classRanking) : undefined,
        notes: formData.notes || undefined
      }

      await goalService.addExamScore({
        examScore,
        userId: user.id
      })

      setSuccess(true)
      setTimeout(() => {
        onSave?.()
      }, 1500)

    } catch (error) {
      console.error('保存分数失败:', error)
      setError('保存失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">请先登录</h2>
          <p className="text-gray-500">登录后即可录入考试分数</p>
        </div>
      </div>
    )
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">保存成功！</h2>
          <p className="text-gray-500">考试分数已成功录入</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Plus className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">录入分数</h1>
                <p className="text-gray-500">记录您的考试成绩</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 表单 */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6 space-y-6">
            {/* 基本信息 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Calendar className="w-5 h-5 mr-2 text-blue-500" />
                考试信息
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    考试名称 *
                  </label>
                  <input
                    type="text"
                    value={formData.examName}
                    onChange={(e) => handleInputChange('examName', e.target.value)}
                    placeholder="如：第一次月考"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    考试日期
                  </label>
                  <input
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* 分数信息 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <BookOpen className="w-5 h-5 mr-2 text-green-500" />
                  分数详情
                </h3>
                <button
                  type="button"
                  onClick={calculateTotalScore}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  自动计算总分
                </button>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  总分 *
                </label>
                <input
                  type="number"
                  value={formData.totalScore}
                  onChange={(e) => handleInputChange('totalScore', e.target.value)}
                  placeholder="750"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(formData.subjects).map(([subject, score]) => (
                  <div key={subject}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {subject}
                    </label>
                    <input
                      type="number"
                      value={score}
                      onChange={(e) => handleSubjectChange(subject, e.target.value)}
                      placeholder="150"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* 排名信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  年级排名
                </label>
                <input
                  type="number"
                  value={formData.ranking}
                  onChange={(e) => handleInputChange('ranking', e.target.value)}
                  placeholder="如：50"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  班级排名
                </label>
                <input
                  type="number"
                  value={formData.classRanking}
                  onChange={(e) => handleInputChange('classRanking', e.target.value)}
                  placeholder="如：5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* 备注 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                备注
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="记录一些关于这次考试的想法..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center">
                  <AlertCircle className="w-4 h-4 text-red-500 mr-2" />
                  <span className="text-sm text-red-600">{error}</span>
                </div>
              </div>
            )}

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={onBack}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={saving}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>保存中...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>保存</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

import React from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import type { SearchFilters as SearchFiltersType } from '../../types/university'
import {
  COLLEGE_CATEGORIES,
  COLLEGE_TYPES,
  EDU_LEVELS,
  COLLEGE_PROPERTIES
} from '../../services/universityApi'
import {
  Search,
  X,
  GraduationCap,
  Building,
  TrendingUp
} from 'lucide-react'

interface SearchFiltersProps {
  filters: SearchFiltersType
  onFiltersChange: (filters: SearchFiltersType) => void
  className?: string
}

export function SearchFilters({ filters, onFiltersChange, className }: SearchFiltersProps) {

  const updateFilter = (key: keyof SearchFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      keywords: filters.keywords,
      pageindex: 1,
      pagesize: filters.pagesize
    })
  }

  const hasActiveFilters = !!(
    filters.collegecategory ||
    filters.collegetype ||
    filters.is985 ||
    filters.is211 ||
    filters.isdualclass ||
    filters.edulevel ||
    filters.collegeproperty ||
    filters.keywordstrict
  )

  return (
    <Card className={cn("p-4", className)}>
      {/* 搜索框 */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="搜索大学名称、地区..."
          value={filters.keywords || ''}
          onChange={(e) => updateFilter('keywords', e.target.value || undefined)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <label className="flex items-center text-xs text-gray-500">
            <input
              type="checkbox"
              checked={filters.keywordstrict || false}
              onChange={(e) => updateFilter('keywordstrict', e.target.checked || undefined)}
              className="mr-1 w-3 h-3"
            />
            精确匹配
          </label>
        </div>
      </div>

      {/* 快速筛选 */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button
          variant={filters.is985 ? 'default' : 'outline'}
          size="sm"
          onClick={() => updateFilter('is985', filters.is985 ? undefined : true)}
          className="text-xs"
        >
          985高校
        </Button>
        <Button
          variant={filters.is211 ? 'default' : 'outline'}
          size="sm"
          onClick={() => updateFilter('is211', filters.is211 ? undefined : true)}
          className="text-xs"
        >
          211高校
        </Button>
        <Button
          variant={filters.isdualclass ? 'default' : 'outline'}
          size="sm"
          onClick={() => updateFilter('isdualclass', filters.isdualclass ? undefined : true)}
          className="text-xs"
        >
          双一流
        </Button>
        <Button
          variant={filters.collegeproperty === '公办' ? 'default' : 'outline'}
          size="sm"
          onClick={() => updateFilter('collegeproperty', filters.collegeproperty === '公办' ? undefined : '公办')}
          className="text-xs"
        >
          公办
        </Button>
      </div>

      {/* 筛选操作 */}
      {hasActiveFilters && (
        <div className="flex justify-end mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-red-600 hover:text-red-700 flex items-center gap-1"
          >
            <X className="w-4 h-4" />
            清除筛选
          </Button>
        </div>
      )}

      {/* 筛选选项 */}
      <div className="space-y-4 border-t border-gray-200 pt-4">
          {/* 院校类别筛选 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <GraduationCap className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">院校类别</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.collegecategory ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('collegecategory', undefined)}
                className="text-sm"
              >
                全部类别
              </Button>
              {COLLEGE_CATEGORIES.map((category) => (
                <Button
                  key={category}
                  variant={filters.collegecategory === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilter('collegecategory', category)}
                  className="text-sm"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* 院校性质 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Building className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">院校性质</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.collegetype ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('collegetype', undefined)}
                className="text-sm"
              >
                全部性质
              </Button>
              {COLLEGE_TYPES.map((type) => (
                <Button
                  key={type}
                  variant={filters.collegetype === type ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilter('collegetype', type)}
                  className="text-sm"
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          {/* 学制 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <GraduationCap className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">学制</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.edulevel ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('edulevel', undefined)}
                className="text-sm"
              >
                全部学制
              </Button>
              {EDU_LEVELS.map((level) => (
                <Button
                  key={level}
                  variant={filters.edulevel === level ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilter('edulevel', level)}
                  className="text-sm"
                >
                  {level}
                </Button>
              ))}
            </div>
          </div>

          {/* 院校资质 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">院校资质</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.collegeproperty ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('collegeproperty', undefined)}
                className="text-sm"
              >
                全部资质
              </Button>
              {COLLEGE_PROPERTIES.map((property) => (
                <Button
                  key={property}
                  variant={filters.collegeproperty === property ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilter('collegeproperty', property)}
                  className="text-sm"
                >
                  {property}
                </Button>
              ))}
            </div>
          </div>
      </div>
    </Card>
  )
}

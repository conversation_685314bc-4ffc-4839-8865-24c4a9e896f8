import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { authService } from '../services/authService'

// 本地类型定义
interface User {
  id: string
  username?: string
  email?: string
  phone?: string
  avatar?: string
  nickname?: string
  province?: string
  subjects?: string[]
  score?: number
  ranking?: number
  createdAt: string
  updatedAt: string
}

type LoginMethod = 'phone' | 'email' | 'wechat'
type RegisterMethod = 'phone' | 'email' | 'username'

interface LoginRequest {
  method: LoginMethod
  phone?: string
  email?: string
  password?: string
  verificationCode?: string
  wechatCode?: string
}

interface RegisterRequest {
  method: RegisterMethod
  username?: string
  email?: string
  phone?: string
  password: string
  confirmPassword: string
  verificationCode?: string
  nickname?: string
}

interface VerificationCodeRequest {
  type: 'login' | 'register'
  phone?: string
  email?: string
}

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (data: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => void
  sendVerificationCode: (data: VerificationCodeRequest) => Promise<void>
  updateUser: (data: Partial<User>) => Promise<void>
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证提供者组件的Props
interface AuthProviderProps {
  children: ReactNode
}

// 认证提供者组件
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // 计算是否已认证
  const isAuthenticated = user !== null

  // 初始化时检查本地存储的用户信息
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedUser = authService.getStoredUser()
        const token = authService.getToken()
        
        if (storedUser && token) {
          // 验证token是否有效
          const isValid = await authService.validateToken()
          if (isValid) {
            setUser(storedUser)
          } else {
            // token无效，清除本地数据
            authService.clearAuthData()
          }
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error)
        authService.clearAuthData()
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [])

  // 登录函数
  const login = async (data: LoginRequest) => {
    try {
      setIsLoading(true)
      const response = await authService.login(data)
      setUser(response.user)
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // 注册函数
  const register = async (data: RegisterRequest) => {
    try {
      setIsLoading(true)
      const response = await authService.register(data)
      setUser(response.user)
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // 登出函数
  const logout = () => {
    authService.logout()
    setUser(null)
  }

  // 发送验证码函数
  const sendVerificationCode = async (data: VerificationCodeRequest) => {
    try {
      await authService.sendVerificationCode(data)
    } catch (error) {
      console.error('发送验证码失败:', error)
      throw error
    }
  }

  // 更新用户信息函数
  const updateUser = async (data: Partial<User>) => {
    try {
      const updatedUser = await authService.updateUser(data)
      setUser(updatedUser)
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  // 上下文值
  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    sendVerificationCode,
    updateUser
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// 使用认证上下文的Hook
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// 导出上下文（用于测试等场景）
export { AuthContext }

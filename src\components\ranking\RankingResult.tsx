import { Card } from '../ui/card'
import { cn } from '../../lib/utils'

// 本地类型定义
interface RankingResult {
  year: number
  province: string
  score: number
  category: '文科' | '理科' | '综合'
  rankingRange: {
    min: number
    max: number
  }
  sameScoreCount: number
  maxRanking: number
  controlLine: {
    type: '一本' | '二本' | '专科' | '高'
    score: number
  }
  percentile?: number
}
import { 
  TrendingUp, 
  Users, 
  Target, 
  Award,
  BarChart3,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'

interface RankingResultProps {
  result: RankingResult
}

export function RankingResult({ result }: RankingResultProps) {
  const formatRanking = (ranking: number) => {
    return ranking.toLocaleString()
  }

  const getBatchColor = (type: string) => {
    switch (type) {
      case '高':
      case '一本':
        return 'text-red-600 bg-red-50 border-red-200'
      case '二本':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case '专科':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getBatchIcon = (type: string) => {
    switch (type) {
      case '高':
      case '一本':
        return <ArrowUp className="w-4 h-4" />
      case '二本':
        return <Minus className="w-4 h-4" />
      case '专科':
        return <ArrowDown className="w-4 h-4" />
      default:
        return <Target className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* 主要结果卡片 */}
      <Card className="p-6 shadow-lg border-orange-100 bg-gradient-to-br from-white to-orange-50">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-4 h-4 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">位次查询结果</h2>
            <p className="text-sm text-gray-600">
              {result.year}年 {result.province} {result.category} {result.score}分
            </p>
          </div>
        </div>

        {/* 核心数据展示 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* 位次范围 */}
          <div className="text-center p-6 bg-white rounded-xl border border-orange-100 shadow-sm">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Target className="w-6 h-6 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {formatRanking(result.rankingRange.min)} ~ {formatRanking(result.rankingRange.max)}
            </div>
            <div className="text-sm text-gray-600">位次范围</div>
          </div>

          {/* 同分人数 */}
          <div className="text-center p-6 bg-white rounded-xl border border-orange-100 shadow-sm">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {result.sameScoreCount.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">同分人数</div>
          </div>

          {/* 最高位次 */}
          <div className="text-center p-6 bg-white rounded-xl border border-orange-100 shadow-sm">
            <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <BarChart3 className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {formatRanking(result.maxRanking)}
            </div>
            <div className="text-sm text-gray-600">最高位次</div>
          </div>
        </div>

        {/* 省控线信息 */}
        <div className="bg-white rounded-xl border border-orange-100 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-lg flex items-center justify-center">
                <Award className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <div className="font-medium text-gray-900">省控线信息</div>
                <div className="text-sm text-gray-600">批次线对比</div>
              </div>
            </div>
            
            <div className={cn(
              "flex items-center gap-2 px-4 py-2 rounded-lg border font-medium",
              getBatchColor(result.controlLine.type)
            )}>
              {getBatchIcon(result.controlLine.type)}
              <span className="text-lg">
                {result.controlLine.type === '高' ? '高分段' : result.controlLine.type}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* 详细分析卡片 */}
      <Card className="p-6 shadow-lg border-orange-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-orange-600" />
          详细分析
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 位次说明 */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">位次说明</h4>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                <span className="text-gray-700">您的分数</span>
                <span className="font-medium text-orange-900">{result.score}分</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="text-gray-700">位次范围</span>
                <span className="font-medium text-blue-900">
                  {formatRanking(result.rankingRange.min)} - {formatRanking(result.rankingRange.max)}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="text-gray-700">同分考生</span>
                <span className="font-medium text-green-900">{result.sameScoreCount}人</span>
              </div>
            </div>
          </div>

          {/* 竞争分析 */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">竞争分析</h4>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span className="font-medium text-gray-900">位次优势</span>
                </div>
                <p className="text-gray-700">
                  您的位次在 {result.province} {result.category} 中排名前 
                  {Math.round((result.rankingRange.min / 500000) * 100)}%
                </p>
              </div>
              
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium text-gray-900">录取建议</span>
                </div>
                <p className="text-gray-700">
                  {result.controlLine.type === '高' || result.controlLine.type === '一本' 
                    ? '可以考虑重点院校和优势专业'
                    : result.controlLine.type === '二本'
                    ? '建议关注二本院校的特色专业'
                    : '可以选择高职院校的热门专业'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import { 
  Globe, 
  Trophy, 
  TrendingUp, 
  Users,
  BarChart3,
  Award
} from 'lucide-react'

interface QSRankingStatsProps {
  totalUniversities: number
  filteredCount: number
  topCountries: Array<{
    country: string
    count: number
  }>
}

export function QSRankingStats({ 
  totalUniversities, 
  filteredCount, 
  topCountries 
}: QSRankingStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {/* Total Universities */}
      <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-blue-700">全球大学总数</p>
            <p className="text-2xl font-bold text-blue-900">{totalUniversities.toLocaleString()}</p>
          </div>
          <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
            <Globe className="w-6 h-6 text-white" />
          </div>
        </div>
      </Card>

      {/* Filtered Count */}
      <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-green-700">筛选结果</p>
            <p className="text-2xl font-bold text-green-900">{filteredCount.toLocaleString()}</p>
          </div>
          <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
            <BarChart3 className="w-6 h-6 text-white" />
          </div>
        </div>
      </Card>

      {/* Top Ranking */}
      <Card className="p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-yellow-700">排名第一</p>
            <p className="text-lg font-bold text-yellow-900">MIT</p>
            <p className="text-xs text-yellow-600">麻省理工学院</p>
          </div>
          <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
            <Trophy className="w-6 h-6 text-white" />
          </div>
        </div>
      </Card>

      {/* Update Status */}
      <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-purple-700">数据更新</p>
            <p className="text-lg font-bold text-purple-900">2024年</p>
            <p className="text-xs text-purple-600">最新排名</p>
          </div>
          <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-6 h-6 text-white" />
          </div>
        </div>
      </Card>

      {/* Top Countries Distribution */}
      {topCountries.length > 0 && (
        <Card className="md:col-span-2 lg:col-span-4 p-6">
          <div className="flex items-center gap-3 mb-4">
            <Award className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">国家分布</h3>
            <span className="text-sm text-gray-500">（当前筛选结果）</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {topCountries.map((country, index) => (
              <div
                key={country.country}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold",
                    index === 0 && "bg-yellow-500",
                    index === 1 && "bg-gray-400",
                    index === 2 && "bg-orange-500",
                    index > 2 && "bg-blue-500"
                  )}>
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 text-sm">
                      {country.country}
                    </p>
                    <p className="text-xs text-gray-500">{country.count} 所大学</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* QS Ranking Indicators */}
      <Card className="md:col-span-2 lg:col-span-4 p-6">
        <div className="flex items-center gap-3 mb-4">
          <BarChart3 className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">QS排名评估指标</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-blue-900">学术声誉</p>
              <p className="text-sm text-blue-700">权重 40%</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
            <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-green-900">师生比例</p>
              <p className="text-sm text-green-700">权重 20%</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
            <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-purple-900">引用率</p>
              <p className="text-sm text-purple-700">权重 20%</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
            <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
              <Award className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-orange-900">雇主声誉</p>
              <p className="text-sm text-orange-700">权重 10%</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-teal-50 rounded-lg">
            <div className="w-10 h-10 bg-teal-500 rounded-lg flex items-center justify-center">
              <Globe className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-teal-900">国际教师</p>
              <p className="text-sm text-teal-700">权重 5%</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-pink-50 rounded-lg">
            <div className="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-pink-900">国际学生</p>
              <p className="text-sm text-pink-700">权重 5%</p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}



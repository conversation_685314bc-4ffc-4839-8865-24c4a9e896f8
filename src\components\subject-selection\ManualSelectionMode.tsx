import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import { 
  BookOpenCheck, 
  Target,
  TrendingUp,
  BookOpen,
  Lightbulb,
  CheckCircle,
  RotateCcw,
  Share2,
  Download
} from 'lucide-react'

// 导入现有组件
import { SubjectSelector } from './SubjectSelector'
import { CombinationAnalysis } from './CombinationAnalysis'
import { MajorRecommendation } from './MajorRecommendation'
import { SelectionGuide } from './SelectionGuide'

// 导入类型
import type { UserSelection } from './types'

interface ManualSelectionModeProps {
  userSelection: UserSelection
  onSelectionChange: (selection: UserSelection) => void
  activeTab: 'selector' | 'analysis' | 'majors' | 'guide'
  onTabChange: (tab: 'selector' | 'analysis' | 'majors' | 'guide') => void
}

export function ManualSelectionMode({ 
  userSelection, 
  onSelectionChange, 
  activeTab, 
  onTabChange 
}: ManualSelectionModeProps) {
  
  // 重置选择
  const handleReset = () => {
    onSelectionChange({
      firstChoice: null,
      secondChoices: [],
      isComplete: false
    })
    onTabChange('selector')
  }

  // 分享选科结果
  const handleShare = () => {
    if (userSelection.isComplete) {
      const text = `我的高考选科：${userSelection.firstChoice} + ${userSelection.secondChoices.join(' + ')}`
      if (navigator.share) {
        navigator.share({
          title: '我的高考选科',
          text: text,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(text)
        alert('选科结果已复制到剪贴板')
      }
    }
  }

  // 导出选科报告
  const handleExport = () => {
    if (userSelection.isComplete) {
      // 这里可以实现导出PDF功能
      alert('导出功能开发中...')
    }
  }

  const tabs = [
    {
      id: 'selector',
      name: '选择科目',
      icon: BookOpenCheck,
      description: '选择首选和再选科目'
    },
    {
      id: 'analysis',
      name: '组合分析',
      icon: Target,
      description: '分析选科组合优劣'
    },
    {
      id: 'majors',
      name: '专业推荐',
      icon: BookOpen,
      description: '查看可报考专业'
    },
    {
      id: 'guide',
      name: '选科指导',
      icon: Lightbulb,
      description: '获取专业选科建议'
    }
  ]

  return (
    <div className="space-y-8">
      {/* 选科状态概览 */}
      <Card className="bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-orange-500" />
            选科状态
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  userSelection.firstChoice ? "bg-green-500" : "bg-gray-300"
                )} />
                <span className="text-sm font-medium">首选科目</span>
                {userSelection.firstChoice && (
                  <Badge className="bg-orange-500 text-white">
                    {userSelection.firstChoice}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  userSelection.secondChoices.length === 2 ? "bg-green-500" : "bg-gray-300"
                )} />
                <span className="text-sm font-medium">再选科目</span>
                <span className="text-sm text-gray-500">
                  ({userSelection.secondChoices.length}/2)
                </span>
                {userSelection.secondChoices.map(subject => (
                  <Badge key={subject} className="bg-green-500 text-white">
                    {subject}
                  </Badge>
                ))}
              </div>
              
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-3 h-3 rounded-full",
                  userSelection.isComplete ? "bg-green-500" : "bg-gray-300"
                )} />
                <span className="text-sm font-medium">
                  {userSelection.isComplete ? '选科完成' : '选科进行中'}
                </span>
                {userSelection.isComplete && (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                )}
              </div>
            </div>
            
            {/* 操作按钮 */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center gap-1"
              >
                <RotateCcw className="w-4 h-4" />
                重新选择
              </Button>
              
              {userSelection.isComplete && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleShare}
                    className="flex items-center gap-1"
                  >
                    <Share2 className="w-4 h-4" />
                    分享
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExport}
                    className="flex items-center gap-1"
                  >
                    <Download className="w-4 h-4" />
                    导出报告
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 标签页导航 */}
      <Card>
        <CardContent className="p-0">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                const isDisabled = tab.id !== 'selector' && tab.id !== 'guide' && !userSelection.isComplete
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => !isDisabled && onTabChange(tab.id as any)}
                    disabled={isDisabled}
                    className={cn(
                      "flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors",
                      isActive
                        ? "border-orange-500 text-orange-600"
                        : isDisabled
                        ? "border-transparent text-gray-400 cursor-not-allowed"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    )}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                    {isDisabled && tab.id !== 'guide' && (
                      <Badge variant="outline" className="text-xs">
                        需完成选科
                      </Badge>
                    )}
                  </button>
                )
              })}
            </nav>
          </div>
        </CardContent>
      </Card>

      {/* 内容区域 */}
      <div className="space-y-8">
        {activeTab === 'selector' && (
          <SubjectSelector
            selection={userSelection}
            onSelectionChange={onSelectionChange}
          />
        )}
        
        {activeTab === 'analysis' && (
          <CombinationAnalysis selection={userSelection} />
        )}
        
        {activeTab === 'majors' && (
          <MajorRecommendation selection={userSelection} />
        )}

        {activeTab === 'guide' && (
          <SelectionGuide />
        )}
      </div>

      {/* 进度提示 */}
      {!userSelection.isComplete && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <TrendingUp className="w-6 h-6 text-blue-500 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">选科进度提示</h3>
                <div className="space-y-2 text-sm text-blue-800">
                  {!userSelection.firstChoice && (
                    <p>• 请先选择首选科目（物理或历史）</p>
                  )}
                  {userSelection.firstChoice && userSelection.secondChoices.length < 2 && (
                    <p>• 请选择2门再选科目（化学、生物、政治、地理中选2门）</p>
                  )}
                  {userSelection.firstChoice && userSelection.secondChoices.length === 1 && (
                    <p>• 还需要选择1门再选科目</p>
                  )}
                  <p>• 完成选科后可查看组合分析和专业推荐</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 帮助信息 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <TrendingUp className="w-6 h-6 text-blue-500 flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-semibold text-blue-900 mb-2">新高考选科说明</h3>
              <div className="space-y-2 text-sm text-blue-800">
                <p>• <strong>3+1+2模式：</strong>语文、数学、外语为必考科目</p>
                <p>• <strong>首选科目：</strong>物理、历史中选择1科</p>
                <p>• <strong>再选科目：</strong>化学、生物、政治、地理中选择2科</p>
                <p>• <strong>总分构成：</strong>3门必考科目 + 1门首选科目 + 2门再选科目，满分750分</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

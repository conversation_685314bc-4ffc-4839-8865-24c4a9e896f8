# 招生计划功能说明

## 功能概述

招生计划功能已成功替换原有的"帮助中心"功能，为用户提供全面的高校招生计划查询服务。该功能基于docs/招生计划.md文档中的API接口规范实现。

## 主要特性

### 1. 双重查询模式
- **按学校分组**: 查看各高校的招生计划信息
- **按专业分组**: 查看各专业的招生计划分布

### 2. 强大的搜索和筛选功能
- **智能搜索**: 支持学校名称或专业名称的模糊搜索
- **省份筛选**: 按省份筛选招生计划
- **专业大类筛选**: 按学科门类筛选
- **年份筛选**: 支持查看2022-2024年及全部年份数据

### 3. 丰富的数据展示
#### 学校分组视图显示：
- 学校基本信息（名称、地址、联系方式）
- 招生规模（总招生人数、专业数量）
- 学校标签（985、211、双一流）
- 历年招生数据对比
- 学校排名信息

#### 专业分组视图显示：
- 专业基本信息（名称、学科分类）
- 招生规模（总招生人数、开设院校数）
- 历年招生数据趋势

### 4. 用户友好的界面设计
- **响应式布局**: 适配桌面和移动设备
- **现代化UI**: 使用卡片式布局和渐变背景
- **直观的数据可视化**: 清晰的数据展示和标签系统
- **便捷的操作**: 一键搜索、重置功能

## 技术实现

### 文件结构
```
src/components/enrollment-plan/
├── EnrollmentPlanPage.tsx          # 主页面组件
└── README.md                       # 功能说明文档

src/services/
└── enrollmentPlanService.ts        # API服务封装
```

### 核心组件

#### EnrollmentPlanPage.tsx
- 主要的招生计划查询页面
- 集成搜索、筛选、分页功能
- 支持学校和专业两种分组模式
- 响应式设计，适配各种屏幕尺寸

#### enrollmentPlanService.ts
- 封装招生计划API调用
- 提供TypeScript类型定义
- 包含模拟数据用于开发测试
- 支持真实API和Mock数据切换

### API接口集成

基于 `/admin-api/system/college-enrollment-plan/group-query` 接口：

**支持的查询参数：**
- `pageNo`: 页码
- `pageSize`: 每页数据量
- `groupType`: 分组类型（school/major）
- `collegeMajorName`: 专业名称模糊查询
- `schoolName`: 学校名称模糊查询
- `provinceName`: 省份筛选
- `classOne`: 专业大类筛选
- `year`: 年份筛选

## 使用方法

### 基本使用流程
1. 在首页导航中点击"招生计划"按钮
2. 选择查询方式（按学校分组 或 按专业分组）
3. 使用搜索框输入关键词
4. 选择筛选条件（省份、专业类别、年份）
5. 点击"搜索"按钮查看结果
6. 浏览招生计划数据，支持分页查看

### 高级功能
- **组合筛选**: 可同时使用多个筛选条件
- **实时搜索**: 支持回车键快速搜索
- **数据重置**: 一键清空所有筛选条件
- **分页浏览**: 支持大量数据的分页展示

## 数据说明

### 学校分组数据包含：
- 基本信息：学校名称、代码、地址、联系方式
- 招生数据：近三年招生人数、专业数量
- 学校属性：985/211/双一流标识、学校类型、排名
- 详细信息：学校简介、收费标准等

### 专业分组数据包含：
- 专业信息：专业名称、学科分类
- 招生统计：近三年招生人数、开设院校数量
- 趋势分析：各年度数据对比

## 开发说明

### 环境配置
无需额外配置，组件已集成到现有项目中。

### 数据源切换
在 `enrollmentPlanService.ts` 中：
- `getEnrollmentPlanData()`: 真实API调用
- `getMockData()`: 模拟数据（当前使用）

### 自定义扩展
- 可在筛选条件中添加更多选项
- 支持自定义数据展示格式
- 可扩展导出功能等

## 注意事项

1. **API认证**: 真实环境需要配置认证Token和租户ID
2. **数据更新**: 当前使用模拟数据，实际部署需切换到真实API
3. **性能优化**: 大量数据时建议启用虚拟滚动
4. **错误处理**: 已包含基本错误处理，可根据需要扩展

## 更新日志

### v1.0.0 (2024-07-24)
- ✅ 完成招生计划功能开发
- ✅ 替换原有帮助中心功能
- ✅ 实现按学校/专业分组查询
- ✅ 集成搜索和筛选功能
- ✅ 添加响应式UI设计
- ✅ 提供模拟数据用于测试

## 测试建议

1. **功能测试**:
   - 测试学校/专业分组切换
   - 验证搜索和筛选功能
   - 检查分页功能
   - 测试响应式布局

2. **数据测试**:
   - 验证数据展示正确性
   - 测试空数据状态
   - 检查加载状态显示

3. **用户体验测试**:
   - 测试操作流畅性
   - 验证界面美观度
   - 检查错误提示友好性

import { useState, useEffect, useCallback } from 'react'
import { ArrowLeft, Search, School, BookOpen, TrendingUp, Users, MapPin, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Card } from '../ui/card'
import { Badge } from '../ui/badge'
import { enrollmentPlanService, type EnrollmentPlanQueryParams, type SchoolGroupInfo, type MajorGroupInfo } from '../../services/enrollmentPlanService'

interface EnrollmentPlanPageProps {
  onBack?: () => void
}

export function EnrollmentPlanPage({ onBack }: EnrollmentPlanPageProps) {
  const [groupType, setGroupType] = useState<'school' | 'major'>('school')
  const [loading, setLoading] = useState(false)
  const [schoolData, setSchoolData] = useState<SchoolGroupInfo[]>([])
  const [majorData, setMajorData] = useState<MajorGroupInfo[]>([])
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  
  // 搜索和筛选状态
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProvince, setSelectedProvince] = useState('安徽')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedYear, setSelectedYear] = useState<number>(2025)
  
  // 展开状态管理
  const [expandedIntros, setExpandedIntros] = useState<Set<string>>(new Set())
  const [expandedExpenses, setExpandedExpenses] = useState<Set<string>>(new Set())

  // 省份选项
  const provinces = [
    '北京', '上海', '天津', '重庆', '河北', '山西', '辽宁', '吉林', '黑龙江',
    '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南',
    '广东', '广西', '海南', '四川', '贵州', '云南', '西藏', '陕西', '甘肃',
    '青海', '宁夏', '新疆', '内蒙古'
  ]

  // 专业大类选项
  const majorCategories = [
    '哲学', '经济学', '法学', '教育学', '文学', '历史学',
    '理学', '工学', '农学', '医学', '管理学', '艺术学'
  ]

  // 年份选项
  const years = [
    { value: 0, label: '全部年份' },
    { value: 2025, label: '2025年' },
    { value: 2024, label: '2024年' },
    { value: 2023, label: '2023年' },
    { value: 2022, label: '2022年' }
  ]

  // 获取数据
  const fetchData = useCallback(async () => {
    setLoading(true)
    try {
      const params: EnrollmentPlanQueryParams = {
        pageNo: currentPage,
        pageSize,
        groupType,
        year: selectedYear || undefined,
        provinceName: selectedProvince || undefined,
        classOne: selectedCategory || undefined,
        schoolName: groupType === 'school' ? searchQuery || undefined : undefined,
        collegeMajorName: groupType === 'major' ? searchQuery || undefined : undefined
      }

      // 调用真实API接口
      const result = await enrollmentPlanService.getEnrollmentPlanData(params)
      
      if (result.groupType === 'school' && result.schoolGroups) {
        setSchoolData(result.schoolGroups.list)
        setTotal(result.schoolGroups.total)
        setMajorData([])
      } else if (result.groupType === 'major' && result.majorGroups) {
        setMajorData(result.majorGroups.list)
        setTotal(result.majorGroups.total)
        setSchoolData([])
      }
    } catch (error) {
      console.error('获取招生计划数据失败:', error)
    } finally {
      setLoading(false)
    }
  }, [groupType, currentPage, pageSize, selectedYear, selectedProvince, selectedCategory, searchQuery])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  // 处理搜索
  const handleSearch = () => {
    setCurrentPage(1)
    fetchData()
  }

  // 重置筛选
  const handleReset = () => {
    setSearchQuery('')
    setSelectedProvince('安徽')
    setSelectedCategory('')
    setSelectedYear(2025)
    setCurrentPage(1)
    fetchData()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* 顶部导航 */}
      <div className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回首页</span>
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-bold text-gray-800">招生计划查询</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 分组类型选择 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <span className="text-sm font-medium text-gray-700">查询方式：</span>
            <div className="flex space-x-2">
              <Button
                variant={groupType === 'school' ? 'default' : 'outline'}
                onClick={() => setGroupType('school')}
                className="flex items-center space-x-2"
              >
                <School className="w-4 h-4" />
                <span>按学校分组</span>
              </Button>
              <Button
                variant={groupType === 'major' ? 'default' : 'outline'}
                onClick={() => setGroupType('major')}
                className="flex items-center space-x-2"
              >
                <BookOpen className="w-4 h-4" />
                <span>按专业分组</span>
              </Button>
            </div>
          </div>
        </div>

        {/* 搜索和筛选区域 */}
        <Card className="p-6 mb-6">
          <div className="space-y-4">
            {/* 第一行：搜索框 */}
            <div className="w-full">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder={groupType === 'school' ? '搜索学校名称...' : '搜索专业名称...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            {/* 第二行：筛选条件和操作按钮 */}
            <div className="flex flex-wrap items-center gap-4">
              {/* 筛选条件 */}
              <div className="flex flex-wrap items-center gap-3">
                {/* 省份筛选 */}
                <div className="min-w-[120px]">
                  <Select
                    value={selectedProvince}
                    onValueChange={setSelectedProvince}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="全部省份" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部省份</SelectItem>
                      {provinces.map(province => (
                        <SelectItem key={province} value={province}>{province}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 专业大类筛选 */}
                <div className="min-w-[120px]">
                  <Select
                    value={selectedCategory}
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="全部类别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">全部类别</SelectItem>
                      {majorCategories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 年份筛选 */}
                <div className="min-w-[120px]">
                  <Select
                    value={selectedYear.toString()}
                    onValueChange={(value) => setSelectedYear(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择年份" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map(year => (
                        <SelectItem key={year.value} value={year.value.toString()}>
                          {year.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2 ml-auto">
                <Button onClick={handleSearch} className="flex items-center space-x-2">
                  <Search className="w-4 h-4" />
                  <span>搜索</span>
                </Button>
                <Button variant="outline" onClick={handleReset}>
                  重置
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* 统计信息 */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                共找到 <span className="font-semibold text-blue-600">{total}</span> 条记录
              </span>
              <Badge variant="outline">
                {groupType === 'school' ? '学校分组' : '专业分组'}
              </Badge>
            </div>
          </div>
        </div>

        {/* 数据列表 */}
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载数据...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {groupType === 'school' ? (
              // 学校分组视图
              schoolData.map((school) => (
                <Card key={school.schoolUuid} className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-start space-x-4 mb-4">
                        {/* 学校校徽图片 */}
                        {school.coverImage && (
                          <div className="flex-shrink-0">
                            <img
                              src={school.coverImage}
                              alt={`${school.schoolName}校徽`}
                              className="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white p-1"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                              }}
                            />
                          </div>
                        )}
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <h3 className="text-lg font-semibold text-gray-800">{school.schoolName}</h3>
                            <div className="flex space-x-2">
                              {school.is985 && <Badge className="bg-red-100 text-red-700">985</Badge>}
                              {school.is211 && <Badge className="bg-blue-100 text-blue-700">211</Badge>}
                              {school.isDualClass && <Badge className="bg-purple-100 text-purple-700">双一流</Badge>}
                              {school.collegeCategory && (
                                <Badge variant="outline" className="text-green-700 border-green-300">
                                  {school.collegeCategory}
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div className="flex items-center space-x-2">
                              <Users className="w-4 h-4 text-blue-500" />
                              <span className="text-sm text-gray-600">
                                总招生: <span className="font-medium">{school.totalEnrollmentNumbers}</span>人
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <BookOpen className="w-4 h-4 text-green-500" />
                              <span className="text-sm text-gray-600">
                                专业数: <span className="font-medium">{school.majorCount}</span>个
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="w-4 h-4 text-orange-500" />
                              <span className="text-sm text-gray-600">{school.province} {school.city} {school.district}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-600">
                                代码: <span className="font-medium">{school.collegeCode}</span>
                              </span>
                            </div>
                          </div>
                          
                          {/* 学校详细信息 */}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm text-gray-600">
                            {school.ranking && (
                               <div className="flex items-center space-x-2">
                                 <TrendingUp className="w-4 h-4 text-purple-500" />
                                 <span>全国排名: <span className="font-medium">{school.ranking}</span></span>
                               </div>
                             )}
                             {school.rankingInCategory && (
                               <div className="flex items-center space-x-2">
                                 <span>类别排名: <span className="font-medium">{school.rankingInCategory}</span></span>
                               </div>
                             )}
                            {school.collegeProperty && (
                              <div className="flex items-center space-x-2">
                                <span>性质: <span className="font-medium">{school.collegeProperty}</span></span>
                              </div>
                            )}
                            {school.eduLevel && (
                              <div className="flex items-center space-x-2">
                                <span>学制: <span className="font-medium">{school.eduLevel}</span></span>
                              </div>
                            )}
                            {(school.oldName || school.shortName) && (
                              <div className="flex items-center space-x-2">
                                <span>
                                  {school.shortName && `简称: ${school.shortName}`}
                                  {school.oldName && school.shortName && ' | '}
                                  {school.oldName && `原名: ${school.oldName}`}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* 联系信息 */}
                      {(school.website || school.callNumber || school.email || school.address) && (
                        <div className="bg-gray-50 rounded-lg p-4 mb-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">联系信息</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                            {school.website && (
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-500">官网:</span>
                                <a
                                  href={school.website}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 underline"
                                >
                                  访问官网
                                </a>
                              </div>
                            )}
                            {school.callNumber && (
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-500">电话:</span>
                                <span>{school.callNumber}</span>
                              </div>
                            )}
                            {school.email && (
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-500">邮箱:</span>
                                <span>{school.email}</span>
                              </div>
                            )}
                            {school.address && (
                              <div className="md:col-span-2">
                                <span className="text-gray-500">地址:</span>
                                <span className="ml-2">{school.address}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* 学校简介 */}
                      {school.intro && (
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium text-gray-700">学校简介</h4>
                            {school.intro.length > 200 && (
                              <button
                                onClick={() => {
                                  const newExpanded = new Set(expandedIntros)
                                  if (expandedIntros.has(school.schoolUuid)) {
                                    newExpanded.delete(school.schoolUuid)
                                  } else {
                                    newExpanded.add(school.schoolUuid)
                                  }
                                  setExpandedIntros(newExpanded)
                                }}
                                className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                              >
                                <span>{expandedIntros.has(school.schoolUuid) ? '收起' : '展开'}</span>
                                {expandedIntros.has(school.schoolUuid) ? (
                                  <ChevronUp className="w-3 h-3" />
                                ) : (
                                  <ChevronDown className="w-3 h-3" />
                                )}
                              </button>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 leading-relaxed">
                            {expandedIntros.has(school.schoolUuid) || school.intro.length <= 200
                              ? school.intro
                              : `${school.intro.substring(0, 200)}...`}
                          </p>
                        </div>
                      )}

                      {/* 历年招生数据 */}
                      <div className="bg-blue-50 rounded-lg p-4 mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-3">历年招生数据</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="text-center">
                            <div className="text-gray-500 mb-1">2025年</div>
                            <div className="font-medium text-blue-600">
                              {school.enrollmentNumbers2025 || 0}人
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              {school.majorCount2025 || 0}个专业
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-500 mb-1">2024年</div>
                            <div className="font-medium text-green-600">
                              {school.enrollmentNumbers2024 || 0}人
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              {school.majorCount2024 || 0}个专业
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-500 mb-1">2023年</div>
                            <div className="font-medium text-orange-600">
                              {school.enrollmentNumbers2023 || 0}人
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              {school.majorCount2023 || 0}个专业
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-500 mb-1">2022年</div>
                            <div className="font-medium text-purple-600">
                              {school.enrollmentNumbers2022 || 0}人
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              {school.majorCount2022 || 0}个专业
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 收费信息 */}
                      {school.expenses && (
                        <div className="bg-yellow-50 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium text-gray-700">收费标准</h4>
                            {school.expenses.length > 150 && (
                              <button
                                onClick={() => {
                                  const newExpanded = new Set(expandedExpenses)
                                  if (expandedExpenses.has(school.schoolUuid)) {
                                    newExpanded.delete(school.schoolUuid)
                                  } else {
                                    newExpanded.add(school.schoolUuid)
                                  }
                                  setExpandedExpenses(newExpanded)
                                }}
                                className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                              >
                                <span>{expandedExpenses.has(school.schoolUuid) ? '收起' : '展开'}</span>
                                {expandedExpenses.has(school.schoolUuid) ? (
                                  <ChevronUp className="w-3 h-3" />
                                ) : (
                                  <ChevronDown className="w-3 h-3" />
                                )}
                              </button>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 leading-relaxed">
                            {expandedExpenses.has(school.schoolUuid) || school.expenses.length <= 150
                              ? school.expenses
                              : `${school.expenses.substring(0, 150)}...`}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))
            ) : null}
          </div>
        )}
      </div>
    </div>
  )
}

// AI聊天相关类型定义

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  animate?: boolean; // 是否应用特殊动画效果
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface CreateConversationRequest {
  app_id: string;
}

export interface CreateConversationResponse {
  request_id: string;
  conversation_id: string;
}

export interface ChatRequest {
  app_id: string;
  query: string;
  stream: boolean;
  conversation_id: string;
  end_user_id?: string;
}

export interface ChatResponse {
  request_id: string;
  date: string;
  answer: string;
  conversation_id: string;
  message_id: string;
  is_completion: boolean;
  content: Array<{
    event_code: number;
    event_message: string;
    event_type: string;
    event_id: string;
    event_status: string;
    content_type: string;
    outputs: {
      text: string | any;
    };
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      name: string;
    };
  }>;
}
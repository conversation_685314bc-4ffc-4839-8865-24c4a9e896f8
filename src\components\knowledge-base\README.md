# 高考知识库功能说明

## 功能概述

高考知识库是一个综合性的高考志愿填报知识平台，为考生和家长提供全方位的专业指导和信息服务。

## 主要功能模块

### 1. 知识库首页 (KnowledgeBasePage)
- **功能**: 知识库总览页面，展示所有知识分类
- **特色**: 
  - 搜索功能，快速定位相关内容
  - 分类展示，清晰的知识结构
  - 统计信息，了解知识库规模
- **导航**: 从首页"高考知识库"按钮进入

### 2. 行业趋势 (IndustryTrendsPage)
- **功能**: 分析各行业发展趋势和就业前景
- **内容包括**:
  - 人工智能、新能源、数字医疗等热门行业分析
  - 就业前景、薪资水平、发展潜力评估
  - 行业分类筛选和搜索功能
- **特色**: 实时更新的行业数据和专业分析

### 3. 志愿100问 (VolunteerQAPage)
- **功能**: 志愿填报常见问题权威解答
- **内容包括**:
  - 基础概念：平行志愿、专业调剂等
  - 政策解读：新高考政策、录取规则等
  - 填报策略：冲稳保策略、数据分析方法等
  - 专业选择：选科要求、专业前景等
- **特色**: 分难度等级，专家权威解答

### 4. 热门专业 (PopularMajorsPage)
- **功能**: 推荐当前最受欢迎的专业
- **内容包括**:
  - 计算机科学、人工智能、数据科学等热门专业
  - 就业率、平均薪资、发展前景分析
  - 核心技能要求和就业方向
  - 推荐院校和入学要求
- **特色**: 多维度排序，全面的专业信息

### 5. 慎选专业 (CautiousMajorsPage)
- **功能**: 提醒需要谨慎考虑的专业
- **内容包括**:
  - 就业困难、竞争激烈的专业分析
  - 市场饱和度、风险等级评估
  - 替代专业推荐
  - 慎选原因和应对建议
- **特色**: 客观分析，理性建议

### 6. 金饭碗 (GoldenJobsPage)
- **功能**: 推荐高薪稳定的优质职业
- **内容包括**:
  - 公务员、银行、互联网、医疗、教育等稳定职业
  - 薪酬水平、职业稳定性、发展潜力分析
  - 入职要求、职业发展路径
  - 相关专业和顶级雇主推荐
- **特色**: 综合评估职业价值

## 技术特点

### 1. 响应式设计
- 适配桌面端和移动端
- 流畅的用户体验

### 2. 搜索功能
- 全文搜索支持
- 智能匹配相关内容

### 3. 分类筛选
- 多维度筛选条件
- 灵活的排序方式

### 4. 数据可视化
- 直观的数据展示
- 图表化的信息呈现

## 使用指南

### 访问方式
1. 启动应用：`npm run dev`
2. 打开浏览器访问 `http://localhost:5175`
3. 点击首页"高考知识库"按钮进入

### 导航流程
```
首页 → 高考知识库 → 选择具体分类
├── 行业趋势
├── 志愿100问  
├── 热门专业
├── 慎选专业
└── 金饭碗
```

### 功能操作
- **搜索**: 在顶部搜索框输入关键词
- **筛选**: 使用左侧筛选条件缩小范围
- **排序**: 选择不同排序方式查看结果
- **返回**: 点击"返回"按钮回到上级页面

## 数据说明

### 数据来源
- 教育部官方数据
- 权威就业报告
- 行业研究机构
- 专家调研分析

### 更新频率
- 基础数据：每季度更新
- 行业趋势：每月更新
- 政策解读：实时更新

## 开发说明

### 文件结构
```
src/components/knowledge-base/
├── KnowledgeBasePage.tsx      # 知识库首页
├── IndustryTrendsPage.tsx     # 行业趋势
├── VolunteerQAPage.tsx        # 志愿100问
├── PopularMajorsPage.tsx      # 热门专业
├── CautiousMajorsPage.tsx     # 慎选专业
├── GoldenJobsPage.tsx         # 金饭碗
└── README.md                  # 说明文档
```

### 组件特点
- TypeScript 类型安全
- React Hooks 状态管理
- Tailwind CSS 样式
- Lucide React 图标库
- 响应式布局设计

### 扩展建议
1. 添加用户收藏功能
2. 实现内容分享功能
3. 增加评论和反馈系统
4. 添加个性化推荐算法
5. 集成更多数据源

## 注意事项

1. **数据准确性**: 所有数据仅供参考，具体情况请以官方发布为准
2. **个人差异**: 专业选择需结合个人兴趣、能力和家庭情况
3. **动态变化**: 行业和专业情况会随时间变化，需持续关注
4. **理性选择**: 避免盲目跟风，做出适合自己的选择

## 联系支持

如有问题或建议，请通过以下方式联系：
- 技术支持：通过应用内反馈功能
- 内容建议：发送邮件至相关部门
- 功能改进：提交GitHub Issue

---

*最后更新时间：2024年1月*

// 行业趋势API服务
export interface IndustryTrendItem {
  id: number
  content: string | null
  type: string
  parentType: string
  question: string
  version: string | null
  createTime: number
  star: string | null
  ds: string
}

export interface IndustryTrendDetailItem {
  id: number
  questionId: number
  content: string
  type: string
  parentType: string
  question: string | null
  version: string | null
  createTime: number
  star: string | null
  ds: string
  dsCus: string
}

export interface IndustryTrendsListResponse {
  code: number
  data: {
    list: IndustryTrendItem[]
    total: number
  }
  msg: string
}

export interface IndustryTrendDetailResponse {
  code: number
  data: {
    list: IndustryTrendDetailItem[]
    total: number
  }
  msg: string
}

// API配置
const API_BASE_URL = 'https://m.kefeichangduo.top'

/**
 * 获取行业趋势列表
 */
export async function getIndustryTrendsList(
  pageNo: number = 1,
  pageSize: number = 20
): Promise<IndustryTrendsListResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question/page?parentType=1&pageNo=${pageNo}&pageSize=${pageSize}`
    
    console.log('🔍 请求行业趋势列表:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: IndustryTrendsListResponse = await response.json()
    console.log('✅ 行业趋势列表响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取行业趋势列表失败:', error)

    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }

    throw error
  }
}

/**
 * 获取行业趋势详情
 */
export async function getIndustryTrendDetail(
  questionId: number,
  version: string = '',
  dsCus: string = ''
): Promise<IndustryTrendDetailResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question-content/page?questionId=${questionId}&version=${version}&dsCus=${encodeURIComponent(dsCus)}`
    
    console.log('🔍 请求行业趋势详情:', url)
    console.log('📋 请求参数:', { questionId, version, dsCus })
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: IndustryTrendDetailResponse = await response.json()
    console.log('✅ 行业趋势详情响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取行业趋势详情失败:', error)

    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }

    throw error
  }
}

/**
 * 格式化时间戳为可读日期
 */
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 中国省份列表
 */
export const CHINA_PROVINCES = [
  '北京', '天津', '河北', '山西', '内蒙古',
  '辽宁', '吉林', '黑龙江', '上海', '江苏',
  '浙江', '安徽', '福建', '江西', '山东',
  '河南', '湖北', '湖南', '广东', '广西',
  '海南', '重庆', '四川', '贵州', '云南',
  '西藏', '陕西', '甘肃', '青海', '宁夏',
  '新疆', '台湾', '香港', '澳门'
]

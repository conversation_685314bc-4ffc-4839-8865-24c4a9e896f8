// 省控线服务
// 基于咕咕数据API: https://www.gugudata.com/api/details/ceeprovince

import { getApiKey, getApiPath, ApiType } from './apiKeys'

export interface ProvinceControlLineRequest {
  appkey: string
  keyword?: string // 省份关键词
  year?: number // 年份
  category?: string // 科类
}

export interface ProvinceControlLineData {
  id: string
  province: string
  year: number
  category: string // 文科/理科/综合
  batchType: string // 本科一批/本科二批/专科
  score: number
  trend?: 'up' | 'down' | 'same'
  trendValue?: number
}

export interface ProvinceControlLineResponse {
  code: number
  message: string
  data: ProvinceControlLineData[]
  total?: number
}

// API配置 - 使用代理路径解决跨域问题
const API_BASE_URL = '/api/gugudata'

// 获取省控线API密钥
const getProvinceApiKey = (): string => {
  try {
    return getApiKey(ApiType.PROVINCE)
  } catch (error) {
    console.warn('获取省控线API密钥失败，使用模拟数据:', error)
    return ''
  }
}

// 获取省控线API路径
const getProvinceApiPath = (): string => {
  try {
    return getApiPath(ApiType.PROVINCE)
  } catch (error) {
    console.warn('获取省控线API路径失败:', error)
    return '/metadata/ceeprovince'
  }
}

/**
 * 获取省控线数据
 * @param params 查询参数
 * @returns Promise<ProvinceControlLineResponse>
 */
export const fetchProvinceControlLines = async (
  params: Omit<ProvinceControlLineRequest, 'appkey'>
): Promise<ProvinceControlLineResponse> => {
  try {
    const apiKey = getProvinceApiKey()
    const apiPath = getProvinceApiPath()
    
    if (!apiKey) {
      console.log('🔄 使用模拟数据 - API密钥未配置')
      return getMockData(params)
    }

    const queryParams = new URLSearchParams({
      appkey: apiKey,
      ...(params.keyword && { keyword: params.keyword }),
      ...(params.year && { year: params.year.toString() }),
      ...(params.category && { category: params.category })
    })

    const url = `${API_BASE_URL}${apiPath}?${queryParams}`
    
    console.log('🌐 调用省控线API:', url.replace(apiKey, '***'))
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    
    // 根据实际API响应格式调整数据处理逻辑
    return {
      code: data.code || 200,
      message: data.message || 'success',
      data: transformApiData(data.data || data.result || []),
      total: data.total
    }
  } catch (error) {
    console.error('Error fetching province control lines:', error)
    // 发生错误时返回模拟数据
    return getMockData(params)
  }
}

/**
 * 转换API数据格式
 * @param apiData 原始API数据
 * @returns 标准化的省控线数据
 */
const transformApiData = (apiData: any[]): ProvinceControlLineData[] => {
  return apiData.map((item, index) => ({
    id: item.id || `${index + 1}`,
    province: item.province || item.provinceName || '',
    year: item.year || new Date().getFullYear(),
    category: item.category || item.subjectType || '',
    batchType: item.batchType || item.batch || '',
    score: item.score || item.controlLine || 0,
    trend: item.trend,
    trendValue: item.trendValue
  }))
}

/**
 * 获取模拟数据（用于开发和测试）
 * @param params 查询参数
 * @returns 模拟的省控线数据
 */
const getMockData = async (
  params: Omit<ProvinceControlLineRequest, 'appkey'>
): Promise<ProvinceControlLineResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500))

  const mockData: ProvinceControlLineData[] = [
    {
      id: '1',
      province: params.keyword || '安徽',
      year: params.year || 2024,
      category: params.category || '理科',
      batchType: '本科一批',
      score: 482,
      trend: 'up',
      trendValue: 5
    },
    {
      id: '2',
      province: params.keyword || '安徽',
      year: params.year || 2024,
      category: params.category || '理科',
      batchType: '本科二批',
      score: 435,
      trend: 'down',
      trendValue: 3
    },
    {
      id: '3',
      province: params.keyword || '安徽',
      year: params.year || 2024,
      category: params.category || '理科',
      batchType: '专科批',
      score: 200,
      trend: 'same'
    }
  ]

  // 根据科类生成不同的数据
  if (params.category === '文科') {
    mockData.forEach(item => {
      item.category = '文科'
      if (item.batchType === '本科一批') item.score = 495
      if (item.batchType === '本科二批') item.score = 440
    })
  } else if (params.category === '综合') {
    mockData.forEach(item => {
      item.category = '综合'
      if (item.batchType === '本科一批') item.score = 488
      if (item.batchType === '本科二批') item.score = 438
    })
  }

  return {
    code: 200,
    message: 'success',
    data: mockData,
    total: mockData.length
  }
}

/**
 * 获取支持的省份列表
 * @returns 省份列表
 */
export const getProvinceList = (): string[] => {
  return [
    '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
    '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
    '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
    '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆'
  ]
}

/**
 * 获取支持的年份列表
 * @returns 年份列表
 */
export const getYearList = (): number[] => {
  const currentYear = new Date().getFullYear()
  return Array.from({ length: 10 }, (_, i) => currentYear - i)
}

/**
 * 获取支持的科类列表
 * @returns 科类列表
 */
export const getCategoryList = (): string[] => {
  return ['理科', '文科', '综合']
}

export default {
  fetchProvinceControlLines,
  getProvinceList,
  getYearList,
  getCategoryList
}
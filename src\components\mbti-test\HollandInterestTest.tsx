import { useState } from "react"
import { ArrowLeft, Heart, ChevronLeft, ChevronRight, Clock, CheckCircle, Wrench, Microscope, Palette, Users, Briefcase, FileText } from "lucide-react"
import { Button } from "../ui/button"
import { Progress } from "../ui/progress"

interface HollandInterestTestProps {
  onBack: () => void
  onNavigate?: (page: string) => void
}

// 模拟测试题目数据
const testQuestions = [
  {
    id: 1,
    question: "我喜欢修理机械或电器设备",
    category: "R"
  },
  {
    id: 2,
    question: "我对科学实验和研究很感兴趣",
    category: "I"
  },
  {
    id: 3,
    question: "我喜欢绘画、音乐或其他艺术活动",
    category: "A"
  },
  {
    id: 4,
    question: "我愿意帮助别人解决个人问题",
    category: "S"
  },
  {
    id: 5,
    question: "我喜欢说服别人接受我的想法",
    category: "E"
  },
  {
    id: 6,
    question: "我喜欢按照明确的指示完成工作",
    category: "C"
  },
  {
    id: 7,
    question: "我喜欢户外工作，如园艺或建筑",
    category: "R"
  },
  {
    id: 8,
    question: "我对数学和统计学很感兴趣",
    category: "I"
  },
  {
    id: 9,
    question: "我有很强的想象力和创造力",
    category: "A"
  },
  {
    id: 10,
    question: "我喜欢教导或培训他人",
    category: "S"
  },
  {
    id: 11,
    question: "我善于组织和管理活动",
    category: "E"
  },
  {
    id: 12,
    question: "我喜欢处理数据和文件",
    category: "C"
  },
  {
    id: 13,
    question: "我喜欢使用工具和机器",
    category: "R"
  },
  {
    id: 14,
    question: "我对生物学和化学很感兴趣",
    category: "I"
  },
  {
    id: 15,
    question: "我喜欢写作或创作故事",
    category: "A"
  },
  {
    id: 16,
    question: "我关心社会问题和公益事业",
    category: "S"
  },
  {
    id: 17,
    question: "我喜欢制定商业计划",
    category: "E"
  },
  {
    id: 18,
    question: "我喜欢按部就班地完成任务",
    category: "C"
  },
  {
    id: 19,
    question: "我对机械原理很感兴趣",
    category: "R"
  },
  {
    id: 20,
    question: "我喜欢进行理论思考和分析",
    category: "I"
  },
  {
    id: 21,
    question: "我对时尚和设计很敏感",
    category: "A"
  },
  {
    id: 22,
    question: "我喜欢与儿童或老人相处",
    category: "S"
  },
  {
    id: 23,
    question: "我有很强的竞争意识",
    category: "E"
  },
  {
    id: 24,
    question: "我喜欢整理和分类信息",
    category: "C"
  }
]

export function HollandInterestTest({ onBack, onNavigate }: HollandInterestTestProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<number, number>>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [startTime] = useState(Date.now())

  const handleAnswer = (score: number) => {
    const newAnswers = { ...answers, [testQuestions[currentQuestion].id]: score }
    setAnswers(newAnswers)

    if (currentQuestion < testQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1)
      }, 200)
    } else {
      setTimeout(() => {
        setIsCompleted(true)
      }, 200)
    }
  }

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleNext = () => {
    if (currentQuestion < testQuestions.length - 1 && answers[testQuestions[currentQuestion].id] !== undefined) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const calculateResults = () => {
    const scores = { R: 0, I: 0, A: 0, S: 0, E: 0, C: 0 }
    
    testQuestions.forEach(question => {
      const answer = answers[question.id]
      if (answer !== undefined) {
        scores[question.category as keyof typeof scores] += answer
      }
    })

    return scores
  }

  const getHollandTypes = () => {
    return {
      R: {
        name: "实用型 (Realistic)",
        icon: Wrench,
        description: "喜欢动手操作，偏好具体的、实际的工作",
        characteristics: ["动手能力强", "喜欢户外活动", "注重实际效果", "偏好技术工作"],
        careers: ["工程师", "技术员", "建筑师", "机械师", "农业专家"],
        color: "bg-blue-500"
      },
      I: {
        name: "研究型 (Investigative)", 
        icon: Microscope,
        description: "喜欢思考分析，偏好理论性和研究性工作",
        characteristics: ["逻辑思维强", "喜欢独立工作", "追求真理", "善于分析"],
        careers: ["科学家", "研究员", "医生", "分析师", "程序员"],
        color: "bg-green-500"
      },
      A: {
        name: "艺术型 (Artistic)",
        icon: Palette,
        description: "富有创造力，喜欢艺术性和创新性工作",
        characteristics: ["想象力丰富", "情感敏感", "追求美感", "独立性强"],
        careers: ["设计师", "艺术家", "作家", "音乐家", "广告创意"],
        color: "bg-purple-500"
      },
      S: {
        name: "社会型 (Social)",
        icon: Users,
        description: "关心他人，喜欢与人交往和帮助他人",
        characteristics: ["善于沟通", "富有同情心", "喜欢合作", "关注他人需求"],
        careers: ["教师", "咨询师", "社工", "护士", "人力资源"],
        color: "bg-orange-500"
      },
      E: {
        name: "企业型 (Enterprising)",
        icon: Briefcase,
        description: "善于领导和管理，喜欢竞争和挑战",
        characteristics: ["领导能力强", "善于说服", "目标导向", "喜欢竞争"],
        careers: ["管理者", "销售员", "律师", "企业家", "政治家"],
        color: "bg-red-500"
      },
      C: {
        name: "常规型 (Conventional)",
        icon: FileText,
        description: "喜欢有序和规范，偏好结构化的工作环境",
        characteristics: ["注重细节", "喜欢秩序", "责任心强", "遵守规则"],
        careers: ["会计师", "银行员", "秘书", "档案员", "统计员"],
        color: "bg-gray-500"
      }
    }
  }

  const progress = ((currentQuestion + 1) / testQuestions.length) * 100
  const elapsedTime = Math.floor((Date.now() - startTime) / 1000 / 60)

  if (isCompleted) {
    const results = calculateResults()
    const hollandTypes = getHollandTypes()
    const sortedResults = Object.entries(results)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)

    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50">
        <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="flex items-center space-x-2 hover:bg-gray-100"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>返回测试中心</span>
                </Button>
                <div className="h-6 w-px bg-gray-300"></div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <h1 className="text-xl font-bold text-gray-800">测试完成</h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 结果概览 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  霍兰德职业兴趣测试结果
                </h2>
                <p className="text-lg text-gray-600">
                  您的职业兴趣类型分析报告
                </p>
              </div>

              {/* 测试统计 */}
              <div className="grid grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600 mb-1">
                    {testQuestions.length}
                  </div>
                  <div className="text-sm text-gray-600">完成题目</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {elapsedTime}
                  </div>
                  <div className="text-sm text-gray-600">用时(分钟)</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {hollandTypes[sortedResults[0][0] as keyof typeof hollandTypes].name.split(' ')[0]}
                  </div>
                  <div className="text-sm text-gray-600">主要类型</div>
                </div>
              </div>

              {/* 兴趣类型分析 */}
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">您的兴趣类型排序</h3>
                {sortedResults.map(([type, score], index) => {
                  const typeInfo = hollandTypes[type as keyof typeof hollandTypes]
                  const IconComponent = typeInfo.icon
                  
                  return (
                    <div key={type} className="p-6 bg-gray-50 rounded-lg">
                      <div className="flex items-start space-x-4 mb-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white ${typeInfo.color}`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-lg font-semibold text-gray-800">
                              {typeInfo.name}
                            </h4>
                            <span className="text-lg font-bold text-gray-600">
                              {score} 分
                            </span>
                          </div>
                          <p className="text-gray-600 mb-3">{typeInfo.description}</p>
                          
                          <div className="mb-4">
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <div 
                                className={`h-3 rounded-full ${typeInfo.color}`}
                                style={{ width: `${(score / Math.max(...Object.values(results))) * 100}%` }}
                              ></div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h5 className="font-medium text-gray-800 mb-2">性格特征：</h5>
                              <div className="flex flex-wrap gap-1">
                                {typeInfo.characteristics.map(char => (
                                  <span key={char} className="px-2 py-1 bg-white text-gray-600 text-xs rounded-full border">
                                    {char}
                                  </span>
                                ))}
                              </div>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-800 mb-2">适合职业：</h5>
                              <div className="flex flex-wrap gap-1">
                                {typeInfo.careers.map(career => (
                                  <span key={career} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                                    {career}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* 职业建议 */}
              <div className="mt-8 p-6 bg-gradient-to-r from-red-50 to-orange-50 rounded-lg">
                <h3 className="text-lg font-bold text-gray-800 mb-4">职业发展建议</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">专业选择建议</h4>
                    <p className="text-sm text-gray-600 mb-2">
                      根据您的主要兴趣类型 <strong>{hollandTypes[sortedResults[0][0] as keyof typeof hollandTypes].name}</strong>，
                      建议选择相关的专业方向。
                    </p>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 重点关注排名前两位的兴趣类型</li>
                      <li>• 考虑兴趣类型的组合特征</li>
                      <li>• 结合个人能力和价值观</li>
                    </ul>
                  </div>
                  <div className="bg-white p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">能力发展方向</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 培养与兴趣类型相关的核心技能</li>
                      <li>• 参与相关的实践活动和项目</li>
                      <li>• 寻找相关领域的实习机会</li>
                      <li>• 与该领域的专业人士交流</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center space-x-4">
              <Button
                onClick={onBack}
                variant="outline"
                className="px-8 py-3"
              >
                返回测试中心
              </Button>
              <Button
                onClick={() => window.print()}
                className="px-8 py-3 bg-red-600 hover:bg-red-700"
              >
                保存报告
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回测试中心</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <Heart className="w-6 h-6 text-red-600" />
                <h1 className="text-xl font-bold text-gray-800">霍兰德职业兴趣测试</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{elapsedTime} 分钟</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 进度条 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              第 {currentQuestion + 1} 题 / 共 {testQuestions.length} 题
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}% 完成
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* 测试内容 */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            {/* 题目 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                {testQuestions[currentQuestion].question}
              </h2>
              
              {/* 选项 */}
              <div className="space-y-3">
                {[
                  { value: 3, label: "非常符合", color: "bg-green-500 hover:bg-green-600" },
                  { value: 2, label: "比较符合", color: "bg-blue-500 hover:bg-blue-600" },
                  { value: 1, label: "一般", color: "bg-gray-500 hover:bg-gray-600" },
                  { value: 0, label: "不符合", color: "bg-red-500 hover:bg-red-600" }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleAnswer(option.value)}
                    className={`w-full p-4 text-white font-medium rounded-lg transition-all duration-200 ${option.color} ${
                      answers[testQuestions[currentQuestion].id] === option.value 
                        ? 'ring-4 ring-red-300 scale-105' 
                        : 'hover:scale-102'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 导航按钮 */}
            <div className="flex justify-between">
              <Button
                onClick={handlePrevious}
                disabled={currentQuestion === 0}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="w-4 h-4" />
                <span>上一题</span>
              </Button>
              
              <Button
                onClick={handleNext}
                disabled={currentQuestion === testQuestions.length - 1 || answers[testQuestions[currentQuestion].id] === undefined}
                className="flex items-center space-x-2"
              >
                <span>下一题</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

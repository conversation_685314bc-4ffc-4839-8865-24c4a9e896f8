// 高考选科相关类型定义

// 科目类型
export type Subject = 
  | '语文' | '数学' | '外语'  // 必考科目
  | '物理' | '历史'          // 首选科目（2选1）
  | '化学' | '生物' | '政治' | '地理'  // 再选科目（4选2）

// 科目分类
export type SubjectCategory = 'required' | 'first-choice' | 'second-choice'

// 科目信息
export interface SubjectInfo {
  id: Subject
  name: string
  category: SubjectCategory
  description: string
  difficulty: 'easy' | 'medium' | 'hard'
  characteristics: string[]
  suitableFor: string[]
  relatedMajors: string[]
}

// 选科组合
export interface SubjectCombination {
  id: string
  name: string
  subjects: Subject[]
  firstChoice: '物理' | '历史'
  secondChoices: Subject[]
  description: string
  advantages: string[]
  disadvantages: string[]
  difficulty: number // 1-5分
  majorCoverage: number // 专业覆盖率百分比
  popularityRank: number // 热门程度排名
  genderRatio: {
    male: number
    female: number
  }
}

// 专业要求
export interface MajorRequirement {
  majorId: string
  majorName: string
  category: string
  requiredSubjects: Subject[]
  preferredSubjects: Subject[]
  description: string
  employmentRate: number
  averageSalary: number
  difficulty: 'easy' | 'medium' | 'hard'
}

// 院校要求
export interface UniversityRequirement {
  universityId: string
  universityName: string
  majorName: string
  requiredSubjects: Subject[]
  minScore: number
  ranking: number
  location: string
  type: '985' | '211' | '双一流' | '普通本科'
}

// 选科建议
export interface SelectionAdvice {
  type: 'strength' | 'interest' | 'career' | 'difficulty'
  title: string
  description: string
  recommendedCombinations: string[]
  reasons: string[]
  priority: number
}

// 用户选科状态
export interface UserSelection {
  firstChoice: '物理' | '历史' | null
  secondChoices: Subject[]
  isComplete: boolean
  combination?: SubjectCombination
}

// 分析结果
export interface AnalysisResult {
  combination: SubjectCombination
  majorMatches: MajorRequirement[]
  universityMatches: UniversityRequirement[]
  advice: SelectionAdvice[]
  competitiveness: {
    score: number
    description: string
    comparison: string
  }
}

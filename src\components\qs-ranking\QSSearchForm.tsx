import { useState } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { qsRankingService } from '../../services/qsRankingService'
import type { QSRankingFilter } from '../../types/qs-ranking'
import { cn } from '../../lib/utils'
import { 
  Search, 
  Filter, 
  Globe, 
  MapPin, 
  Trophy,
  ChevronDown,
  X,
  Loader2
} from 'lucide-react'

interface QSSearchFormProps {
  filter: QSRankingFilter
  onFilterChange: (filter: Partial<QSRankingFilter>) => void
  onSearch: () => void
  isLoading?: boolean
}

export function QSSearchForm({ 
  filter, 
  onFilterChange, 
  onSearch, 
  isLoading = false 
}: QSSearchFormProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [countryDropdownOpen, setCountryDropdownOpen] = useState(false)
  const [regionDropdownOpen, setRegionDropdownOpen] = useState(false)

  const countriesAndRegions = qsRankingService.getAvailableCountriesAndRegions()
  const countries = [...new Set(countriesAndRegions.map(cr => cr.country))].sort()
  const regions = [...new Set(countriesAndRegions.flatMap(cr => cr.regions))].sort()

  const handleSearchNameChange = (value: string) => {
    onFilterChange({ searchName: value })
  }

  const handleCountryChange = (country: string) => {
    onFilterChange({ selectedCountry: country })
    setCountryDropdownOpen(false)
  }

  const handleRegionChange = (region: string) => {
    onFilterChange({ selectedRegion: region })
    setRegionDropdownOpen(false)
  }

  const handleRankRangeChange = (type: 'min' | 'max', value: number) => {
    onFilterChange({
      rankRange: {
        ...filter.rankRange,
        [type]: value
      }
    })
  }

  const clearFilters = () => {
    onFilterChange({
      searchName: '',
      selectedCountry: '',
      selectedRegion: '',
      rankRange: { min: 1, max: 100 },
      showTopOnly: false
    })
  }

  const hasActiveFilters = filter.searchName || filter.selectedCountry || 
    filter.selectedRegion || filter.showTopOnly ||
    filter.rankRange.min !== 1 || filter.rankRange.max !== 100

  return (
    <div className="space-y-4">
      {/* Search Card */}
      <Card className="p-4">
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Search className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">搜索大学</h3>
          </div>

          {/* University Name Search */}
          <div>
            <Label htmlFor="search-name" className="text-sm font-medium text-gray-700">
              大学名称
            </Label>
            <div className="relative mt-1">
              <Input
                id="search-name"
                type="text"
                placeholder="输入大学名称（支持中英文）..."
                value={filter.searchName}
                onChange={(e) => handleSearchNameChange(e.target.value)}
                className="pl-10"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              支持中英文搜索，如：麻省理工、MIT、哈佛大学、Harvard
            </p>
          </div>

          {/* Search Button */}
          <Button
            onClick={onSearch}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                搜索中...
              </>
            ) : (
              <>
                <Search className="w-4 h-4 mr-2" />
                搜索
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* Filters Card */}
      <Card className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">筛选条件</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="text-blue-600 hover:text-blue-700"
            >
              <ChevronDown className={cn(
                "w-4 h-4 transition-transform",
                showAdvancedFilters && "rotate-180"
              )} />
            </Button>
          </div>

          {/* Top Universities Toggle */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="top-only"
              checked={filter.showTopOnly}
              onChange={(e) => onFilterChange({ showTopOnly: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <Label htmlFor="top-only" className="text-sm text-gray-700">
              仅显示前50名
            </Label>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="space-y-4 pt-4 border-t border-gray-200">
              {/* Country Filter */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  国家/地区
                </Label>
                <div className="relative">
                  <Button
                    variant="outline"
                    onClick={() => setCountryDropdownOpen(!countryDropdownOpen)}
                    className="w-full justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4" />
                      <span>{filter.selectedCountry || '选择国家'}</span>
                    </div>
                    <ChevronDown className="w-4 h-4" />
                  </Button>
                  
                  {countryDropdownOpen && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto">
                      <div
                        className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm"
                        onClick={() => handleCountryChange('')}
                      >
                        全部国家
                      </div>
                      {countries.map(country => (
                        <div
                          key={country}
                          className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm"
                          onClick={() => handleCountryChange(country)}
                        >
                          {country}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Region Filter */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  地区
                </Label>
                <div className="relative">
                  <Button
                    variant="outline"
                    onClick={() => setRegionDropdownOpen(!regionDropdownOpen)}
                    className="w-full justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      <span>{filter.selectedRegion || '选择地区'}</span>
                    </div>
                    <ChevronDown className="w-4 h-4" />
                  </Button>
                  
                  {regionDropdownOpen && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                      <div
                        className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm"
                        onClick={() => handleRegionChange('')}
                      >
                        全部地区
                      </div>
                      {regions.map(region => (
                        <div
                          key={region}
                          className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm"
                          onClick={() => handleRegionChange(region)}
                        >
                          {region}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Rank Range */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                  排名范围
                </Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="1"
                    max="1000"
                    value={filter.rankRange.min}
                    onChange={(e) => handleRankRangeChange('min', parseInt(e.target.value) || 1)}
                    className="flex-1"
                    placeholder="最低排名"
                  />
                  <span className="text-gray-500">-</span>
                  <Input
                    type="number"
                    min="1"
                    max="1000"
                    value={filter.rankRange.max}
                    onChange={(e) => handleRankRangeChange('max', parseInt(e.target.value) || 100)}
                    className="flex-1"
                    placeholder="最高排名"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Clear Filters */}
          {hasActiveFilters && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="w-full text-gray-600 hover:text-gray-800"
            >
              <X className="w-4 h-4 mr-2" />
              清除筛选
            </Button>
          )}
        </div>
      </Card>

      {/* Quick Filters */}
      <Card className="p-4">
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">快速筛选</h3>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onFilterChange({ 
                rankRange: { min: 1, max: 10 },
                showTopOnly: false 
              })}
              className="text-xs"
            >
              前10名
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onFilterChange({ 
                selectedRegion: 'Asia',
                showTopOnly: false 
              })}
              className="text-xs"
            >
              亚洲大学
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onFilterChange({ 
                selectedCountry: 'United States',
                showTopOnly: false 
              })}
              className="text-xs"
            >
              美国大学
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onFilterChange({ 
                selectedCountry: 'United Kingdom',
                showTopOnly: false 
              })}
              className="text-xs"
            >
              英国大学
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}

// 测试类型导入
import type { 
  OneScoreOneSectionFilters,
  OneScoreOneSectionItem
} from './types/oneScoreOneSection'

import { 
  getOneScoreOneSectionData, 
  getAvailableYears
} from './services/oneScoreOneSectionApi'

// 测试类型使用
const testFilters: OneScoreOneSectionFilters = {
  year: '2024',
  provincename: '安徽',
  subjectselection: '物理类'
}

console.log('✅ 类型导入测试成功')
console.log('📋 测试过滤器:', testFilters)
console.log('📅 可用年份:', getAvailableYears())

export { testFilters }

import {
  Heart,
  Star,
  <PERSON>hum<PERSON>Up,
  MessageCircle,
  Share2,
  Download,
  Upload,
  Search,
  Settings,
  User,
  Home,
  Mail,
  Phone,
  Calendar,
  Clock,
  MapPin,
  Camera,
  Music,
  Video,
  Image,
  File,
  Folder,
  Edit,
  Trash,
  Save,
  Copy,
  Scissors,
  ClipboardPaste,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  RotateCw,
  RotateCcw,
  Maximize,
  Play,
  Pause,
  Square,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Wifi,
  WifiOff,
  Battery,
  BatteryLow,
  Bluetooth,
  Smartphone,
  Laptop,
  Monitor
} from "lucide-react"

const iconCategories = [
  {
    title: "常用操作",
    icons: [
      { icon: Heart, name: "Heart", color: "text-red-500" },
      { icon: Star, name: "<PERSON>", color: "text-yellow-500" },
      { icon: ThumbsUp, name: "ThumbsUp", color: "text-blue-500" },
      { icon: MessageCircle, name: "Message", color: "text-green-500" },
      { icon: Share2, name: "Share", color: "text-purple-500" },
      { icon: Download, name: "Download", color: "text-indigo-500" },
      { icon: Upload, name: "Upload", color: "text-pink-500" },
      { icon: Search, name: "Search", color: "text-gray-500" }
    ]
  },
  {
    title: "界面元素",
    icons: [
      { icon: Settings, name: "Settings", color: "text-gray-600" },
      { icon: User, name: "User", color: "text-blue-600" },
      { icon: Home, name: "Home", color: "text-green-600" },
      { icon: Mail, name: "Mail", color: "text-red-600" },
      { icon: Phone, name: "Phone", color: "text-emerald-600" },
      { icon: Calendar, name: "Calendar", color: "text-orange-600" },
      { icon: Clock, name: "Clock", color: "text-purple-600" },
      { icon: MapPin, name: "MapPin", color: "text-rose-600" }
    ]
  },
  {
    title: "媒体文件",
    icons: [
      { icon: Camera, name: "Camera", color: "text-slate-600" },
      { icon: Music, name: "Music", color: "text-violet-600" },
      { icon: Video, name: "Video", color: "text-cyan-600" },
      { icon: Image, name: "Image", color: "text-teal-600" },
      { icon: File, name: "File", color: "text-amber-600" },
      { icon: Folder, name: "Folder", color: "text-lime-600" },
      { icon: Edit, name: "Edit", color: "text-sky-600" },
      { icon: Trash, name: "Trash", color: "text-red-500" }
    ]
  },
  {
    title: "编辑操作",
    icons: [
      { icon: Save, name: "Save", color: "text-green-500" },
      { icon: Copy, name: "Copy", color: "text-blue-500" },
      { icon: Scissors, name: "Scissors", color: "text-orange-500" },
      { icon: ClipboardPaste, name: "Paste", color: "text-purple-500" },
      { icon: Undo, name: "Undo", color: "text-gray-500" },
      { icon: Redo, name: "Redo", color: "text-gray-500" },
      { icon: ZoomIn, name: "ZoomIn", color: "text-emerald-500" },
      { icon: ZoomOut, name: "ZoomOut", color: "text-red-500" }
    ]
  },
  {
    title: "媒体控制",
    icons: [
      { icon: Play, name: "Play", color: "text-green-500" },
      { icon: Pause, name: "Pause", color: "text-yellow-500" },
      { icon: Square, name: "Stop", color: "text-red-500" },
      { icon: SkipForward, name: "SkipForward", color: "text-blue-500" },
      { icon: SkipBack, name: "SkipBack", color: "text-blue-500" },
      { icon: Volume2, name: "Volume", color: "text-purple-500" },
      { icon: VolumeX, name: "Mute", color: "text-gray-500" },
      { icon: Maximize, name: "Maximize", color: "text-indigo-500" }
    ]
  },
  {
    title: "设备硬件",
    icons: [
      { icon: Wifi, name: "Wifi", color: "text-green-500" },
      { icon: WifiOff, name: "WifiOff", color: "text-red-500" },
      { icon: Battery, name: "Battery", color: "text-green-500" },
      { icon: BatteryLow, name: "BatteryLow", color: "text-red-500" },
      { icon: Bluetooth, name: "Bluetooth", color: "text-blue-500" },
      { icon: Smartphone, name: "Phone", color: "text-gray-600" },
      { icon: Laptop, name: "Laptop", color: "text-gray-600" },
      { icon: Monitor, name: "Monitor", color: "text-gray-600" }
    ]
  }
]

export function LucideShowcase() {
  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="text-center mb-12">
        <h2 className="text-4xl font-bold mb-4 flex items-center justify-center gap-3">
          <Star className="w-10 h-10 text-yellow-500" />
          Lucide Icons 图标展示
        </h2>
        <p className="text-muted-foreground text-lg">
          1000+ 精美图标，完美适配现代 Web 应用
        </p>
      </div>

      <div className="grid gap-12">
        {iconCategories.map((category, categoryIdx) => (
          <div key={categoryIdx} className="space-y-6">
            <h3 className="text-2xl font-semibold text-center">{category.title}</h3>
            <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-8 gap-6">
              {category.icons.map((iconItem, iconIdx) => {
                const IconComponent = iconItem.icon
                return (
                  <div
                    key={iconIdx}
                    className="flex flex-col items-center p-4 rounded-lg bg-card border hover:shadow-lg transition-all duration-200 hover:scale-105 cursor-pointer group"
                  >
                    <IconComponent className={`w-8 h-8 ${iconItem.color} group-hover:scale-110 transition-transform`} />
                    <span className="text-xs text-muted-foreground mt-2 text-center">
                      {iconItem.name}
                    </span>
                  </div>
                )
              })}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-16 text-center">
        <div className="bg-muted/50 rounded-lg p-6">
          <h3 className="text-xl font-semibold mb-4">如何使用</h3>
          <div className="text-left max-w-2xl mx-auto space-y-4">
            <div className="bg-background rounded p-4 border">
              <p className="text-sm text-muted-foreground mb-2">1. 导入图标：</p>
              <code className="text-sm">
                import &#123; Heart, Star, Settings &#125; from "lucide-react"
              </code>
            </div>
            <div className="bg-background rounded p-4 border">
              <p className="text-sm text-muted-foreground mb-2">2. 使用图标：</p>
              <code className="text-sm">
                &lt;Heart className="w-6 h-6 text-red-500" /&gt;
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

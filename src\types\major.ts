// 专业相关的类型定义

// 学科门类
export type DisciplineCategory =
  | '哲学'
  | '经济学'
  | '法学'
  | '教育学'
  | '文学'
  | '历史学'
  | '理学'
  | '工学'
  | '农学'
  | '医学'
  | '管理学'
  | '艺术学'

// 学位类型
export type DegreeType = '学士' | '硕士' | '博士'

// 专业接口
export interface Major {
  id: string
  code: string // 专业代码，如 "080901"
  name: string // 专业名称
  englishName?: string
  category: DisciplineCategory // 学科门类
  subCategory?: string // 专业类别，如 "计算机类"
  degree: DegreeType // 学位类型
  duration: number // 学制（年）
  description?: string // 专业描述
  
  // 就业相关信息
  employment?: {
    rate?: number // 就业率 (0-100)
    avgSalary?: number // 平均薪资（元/年）
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[] // 主要就业行业
    topPositions?: string[] // 主要就业岗位
  }
  
  // 性别比例
  genderRatio?: {
    male: number // 男性比例 (0-100)
    female: number // 女性比例 (0-100)
  }
  
  // 课程信息
  courses?: {
    core?: string[] // 核心课程
    elective?: string[] // 选修课程
  }
  
  // 相关院校
  universities?: {
    id: string
    name: string
    ranking?: number
    admissionScore?: number
  }[]
  
  // 专业特色
  features?: string[]
  
  // 发展前景
  prospects?: {
    trend: 'rising' | 'stable' | 'declining' // 发展趋势
    demandLevel: 'high' | 'medium' | 'low' // 需求程度
    competitionLevel: 'high' | 'medium' | 'low' // 竞争激烈程度
  }
}

// 专业搜索筛选条件
export interface MajorSearchFilters {
  keyword?: string
  category?: DisciplineCategory[]
  subCategory?: string[]
  degree?: DegreeType[]
  duration?: number[]
  minSalary?: number
  maxSalary?: number
  minEmploymentRate?: number
  genderPreference?: 'male' | 'female' | 'balanced'
  sortBy?: 'name' | 'salary' | 'employment' | 'popularity'
  sortOrder?: 'asc' | 'desc'
}

// 专业搜索结果
export interface MajorSearchResult {
  majors: Major[]
  total: number
  page: number
  pageSize: number
  filters: MajorSearchFilters
}

// 学科分类统计
export interface CategoryStats {
  category: DisciplineCategory
  count: number
  avgSalary?: number
  avgEmploymentRate?: number
}

import { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { goalService } from '../../services/goalService'
import { searchUniversities } from '../../services/universityApi'
import { getCollegeMajor } from '../../services/majorApi'
import { getCollegeScoreLine } from '../../services/scoreLineApiSimple'
import type { UserGoal, UniversityGoal, MajorGoal, ExamScore } from '../../types/goal'
import type { University } from '../../types/university'
import type { CollegeScoreLineData } from '../../services/scoreLineApiSimple'
import {
  Target,
  Search,
  School,
  BookOpen,
  ChevronLeft,
  ChevronRight,
  Star,
  MapPin,
  Award,
  Loader2,
  CheckCircle,
  ArrowLeft
} from 'lucide-react'

interface GoalSettingPageProps {
  onNavigate?: (page: string) => void
  onBack?: () => void
}

type Step = 'type' | 'university' | 'major' | 'confirm'

export function GoalSettingPage({ onNavigate, onBack }: GoalSettingPageProps) {
  const { user, isAuthenticated } = useAuth()
  const [currentStep, setCurrentStep] = useState<Step>('type')
  const [goalType, setGoalType] = useState<'university' | 'major'>('university')
  const [selectedUniversity, setSelectedUniversity] = useState<University | null>(null)
  const [selectedMajor, setSelectedMajor] = useState<any>(null)
  const [targetScore, setTargetScore] = useState<string>('')

  // 搜索状态
  const [universitySearchQuery, setUniversitySearchQuery] = useState('')
  const [majorSearchQuery, setMajorSearchQuery] = useState('')
  const [universities, setUniversities] = useState<University[]>([])
  const [majors, setMajors] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  // 用户分数和录取分数线数据
  const [userLatestScore, setUserLatestScore] = useState<ExamScore | null>(null)
  const [universityScoreLines, setUniversityScoreLines] = useState<Map<string, CollegeScoreLineData[]>>(new Map())
  const [scoreLineLoading, setScoreLineLoading] = useState(false)
  const [admissionFilter, setAdmissionFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all')

  // 使用录取分数线API获取可选择的大学
  const fetchUniversitiesFromScoreLine = async (userScore?: number) => {
    if (!user) return []

    try {
      console.log('🔍 使用录取分数线API获取可选择的大学...')

      // 使用省份查询方式获取该省份所有大学的录取分数线
      const response = await getCollegeScoreLine({
        searchtype: 'PROVINCENAME',
        keyword: user.province || '安徽',
        pageindex: 1,
        pagesize: 20, // 获取更多数据
        year: 2024,
        type: '物理类', // 根据用户科目类型调整
        sort: 'LowestScore|asc' // 按最低分排序
      })

      if (response.DataStatus.StatusCode === 100) {
        const scoreLineData = response.Data || []
        console.log(`✅ 获取到 ${scoreLineData.length} 条录取分数线数据`)

        // 将录取分数线数据转换为大学列表
        const universitiesFromScoreLine = scoreLineData.map(item => ({
          SchoolUUID: item.SchoolUUID,
          CollegeName: item.CollegeName,
          Province: item.Province || '未知',
          City: item.SchoolInCity || '未知',
          Is985: item.Is985,
          Is211: item.Is211,
          IsDualClass: item.IsDualClass,
          CollegeType: item.SchoolType || '未知', // 公办/民办
          CollegeCategory: '综合类',
          CoverImage: item.CoverImage, // 学校logo
          // 添加录取分数线信息
          scoreLineInfo: {
            year: item.Year,
            lowestScore: item.LowestScore === '-' ? 0 : parseInt(item.LowestScore) || 0,
            averageScore: item.AverageScore === '-' ? 0 : parseInt(item.AverageScore) || 0,
            highestScore: item.HighestScore === '-' ? 0 : parseInt(item.HighestScore) || 0,
            lowestRank: item.LowestRank,
            provincialControlLine: item.ProvincialControlLine, // 省控线
            admissionBatch: item.AdmissionBatch,
            typeName: item.TypeName,
            enrollmentType: item.EnrollmentType, // 招生类型
            courseSelection: item.CourseSelection, // 选科要求
            courseSelectionName: item.CourseSelectionName, // 专业组
            subjectGroup: item.SubjectGroup // 科目组
          }
        }))

        // 去重（同一个大学可能有多条记录）
        const uniqueUniversities = universitiesFromScoreLine.reduce((acc, current) => {
          const existing = acc.find(item => item.SchoolUUID === current.SchoolUUID)
          if (!existing) {
            acc.push(current)
          }
          return acc
        }, [] as any[])

        console.log(`📊 去重后获得 ${uniqueUniversities.length} 所大学`)
        return uniqueUniversities
      }
    } catch (error) {
      console.error('❌ 获取录取分数线数据失败:', error)
    }
    return []
  }

  // 搜索大学 - 使用录取分数线API
  const searchUniversitiesData = async (query: string = '') => {
    try {
      setLoading(true)

      let universitiesData: any[] = []

      if (query.trim()) {
        // 如果有搜索关键词，使用大学名称搜索
        console.log('🔍 按大学名称搜索:', query)
        const response = await getCollegeScoreLine({
          searchtype: 'COLLEGENAME',
          keyword: query,
          pageindex: 1,
          pagesize: 20,
          year: 2024,
          enrollprovince: user?.province || '安徽',
          type: '物理类'
        })

        if (response.DataStatus.StatusCode === 100) {
          const scoreLineData = response.Data || []
          universitiesData = scoreLineData.map(item => ({
            SchoolUUID: item.SchoolUUID,
            CollegeName: item.CollegeName,
            Province: item.Province || '未知',
            City: item.SchoolInCity || '未知',
            Is985: item.Is985,
            Is211: item.Is211,
            IsDualClass: item.IsDualClass,
            CollegeType: item.SchoolType || '未知',
            CoverImage: item.CoverImage,
            scoreLineInfo: {
              year: item.Year,
              lowestScore: item.LowestScore === '-' ? 0 : parseInt(item.LowestScore) || 0,
              averageScore: item.AverageScore === '-' ? 0 : parseInt(item.AverageScore) || 0,
              highestScore: item.HighestScore === '-' ? 0 : parseInt(item.HighestScore) || 0,
              lowestRank: item.LowestRank,
              provincialControlLine: item.ProvincialControlLine,
              admissionBatch: item.AdmissionBatch,
              typeName: item.TypeName,
              enrollmentType: item.EnrollmentType,
              courseSelection: item.CourseSelection,
              courseSelectionName: item.CourseSelectionName,
              subjectGroup: item.SubjectGroup
            }
          }))
        }
      } else {
        // 如果没有搜索关键词，获取用户省份的所有大学
        universitiesData = await fetchUniversitiesFromScoreLine(userLatestScore?.totalScore)
      }

      // 去重处理
      const uniqueUniversities = universitiesData.reduce((acc, current) => {
        const existing = acc.find(item => item.SchoolUUID === current.SchoolUUID)
        if (!existing) {
          acc.push(current)
        }
        return acc
      }, [] as any[])

      // 构建录取分数线映射
      const newScoreLineMap = new Map()
      uniqueUniversities.forEach(university => {
        if (university.scoreLineInfo) {
          newScoreLineMap.set(university.SchoolUUID, [university.scoreLineInfo])
        }
      })
      setUniversityScoreLines(newScoreLineMap)

      // 根据录取可能性排序
      if (userLatestScore) {
        const sortedUniversities = [...uniqueUniversities].sort((a, b) => {
          const possibilityA = calculateAdmissionPossibilityForSort(a, newScoreLineMap)
          const possibilityB = calculateAdmissionPossibilityForSort(b, newScoreLineMap)

          const order = { high: 4, medium: 3, low: 2, unknown: 1 }
          return order[possibilityB] - order[possibilityA]
        })
        setUniversities(sortedUniversities)
      } else {
        setUniversities(uniqueUniversities)
      }

      console.log(`📊 最终获得 ${uniqueUniversities.length} 所大学`)

    } catch (error) {
      console.error('❌ 搜索大学失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 用于排序的录取可能性计算（简化版）
  const calculateAdmissionPossibilityForSort = (
    university: any,
    scoreLineMap: Map<string, any[]>
  ): 'high' | 'medium' | 'low' | 'unknown' => {
    if (!userLatestScore) return 'unknown'

    let minScore = 0
    let avgScore = 0

    // 优先使用大学对象中的scoreLineInfo
    if (university.scoreLineInfo) {
      minScore = university.scoreLineInfo.lowestScore || 0
      avgScore = university.scoreLineInfo.averageScore || 0
    } else {
      // 备用：从scoreLineMap中获取
      const scoreLines = scoreLineMap.get(university.SchoolUUID)
      if (!scoreLines || scoreLines.length === 0) return 'unknown'

      const latestScoreLine = scoreLines[0]
      minScore = parseInt(latestScoreLine.LowestScore) || latestScoreLine.lowestScore || 0
      avgScore = parseInt(latestScoreLine.AverageScore) || latestScoreLine.averageScore || 0
    }

    // 如果没有有效分数数据
    if (minScore === 0 && avgScore === 0) return 'unknown'

    const userScore = userLatestScore.totalScore

    // 优先使用平均分判断
    if (avgScore > 0) {
      if (userScore >= avgScore + 20) return 'high'
      if (userScore >= avgScore - 10) return 'medium'
    }

    // 使用最低分判断
    if (minScore > 0) {
      if (userScore >= minScore + 30) return 'high'
      if (userScore >= minScore) return 'medium'
      if (userScore >= minScore - 20) return 'low'
    }

    return 'low'
  }

  // 搜索专业
  const searchMajorsData = async (universityName: string, query: string = '') => {
    try {
      setLoading(true)
      const response = await getCollegeMajor({
        schoolname: universityName,
        keywords: query,
        pagesize: 20,
        pageindex: 1
      })

      if (response.DataStatus?.StatusCode === 100) {
        setMajors(response.Data || [])
      } else {
        // 如果API没有返回数据，使用模拟数据
        const mockMajors = [
          {
            DataId: 'mock_1',
            MajorName: '计算机科学与技术',
            MajorCode: '080901',
            MajorType: '工学',
            Degree: '工学学士'
          },
          {
            DataId: 'mock_2',
            MajorName: '软件工程',
            MajorCode: '080902',
            MajorType: '工学',
            Degree: '工学学士'
          },
          {
            DataId: 'mock_3',
            MajorName: '人工智能',
            MajorCode: '080717T',
            MajorType: '工学',
            Degree: '工学学士'
          },
          {
            DataId: 'mock_4',
            MajorName: '数据科学与大数据技术',
            MajorCode: '080910T',
            MajorType: '工学',
            Degree: '工学学士'
          }
        ].filter(major =>
          !query || major.MajorName.toLowerCase().includes(query.toLowerCase())
        )
        setMajors(mockMajors)
      }
    } catch (error) {
      console.error('搜索专业失败:', error)
      // 使用模拟数据作为后备
      const mockMajors = [
        {
          DataId: 'mock_1',
          MajorName: '计算机科学与技术',
          MajorCode: '080901',
          MajorType: '工学',
          Degree: '工学学士'
        },
        {
          DataId: 'mock_2',
          MajorName: '软件工程',
          MajorCode: '080902',
          MajorType: '工学',
          Degree: '工学学士'
        }
      ]
      setMajors(mockMajors)
    } finally {
      setLoading(false)
    }
  }

  // 获取用户最新考试分数
  useEffect(() => {
    if (user) {
      const scores = goalService.getExamScores(user.id).scores
      if (scores.length > 0) {
        setUserLatestScore(scores[0]) // 最新的分数记录
        console.log('📊 获取到用户最新分数:', scores[0])
      }
    }
  }, [user])

  // 初始加载大学数据
  useEffect(() => {
    if (currentStep === 'university') {
      searchUniversitiesData()
    }
  }, [currentStep])

  // 加载专业数据
  useEffect(() => {
    if (currentStep === 'major' && selectedUniversity) {
      searchMajorsData(selectedUniversity.CollegeName)
    }
  }, [currentStep, selectedUniversity])

  const handleUniversitySearch = (query: string) => {
    setUniversitySearchQuery(query)
    searchUniversitiesData(query)
  }

  const handleMajorSearch = (query: string) => {
    setMajorSearchQuery(query)
    if (selectedUniversity) {
      searchMajorsData(selectedUniversity.CollegeName, query)
    }
  }

  const handleUniversitySelect = (university: University) => {
    setSelectedUniversity(university)
    if (goalType === 'university') {
      setCurrentStep('confirm')
    } else {
      setCurrentStep('major')
    }
  }

  const handleMajorSelect = (major: any) => {
    setSelectedMajor(major)
    setCurrentStep('confirm')
  }

  // 计算录取可能性
  const calculateAdmissionPossibility = (university: any): {
    possibility: 'high' | 'medium' | 'low' | 'unknown'
    label: string
    color: string
    minScore?: number
    avgScore?: number
  } => {
    if (!userLatestScore) {
      return {
        possibility: 'unknown',
        label: '需要分数信息',
        color: 'text-gray-500'
      }
    }

    // 优先使用大学对象中的scoreLineInfo
    let minScore = 0
    let avgScore = 0

    if (university.scoreLineInfo) {
      minScore = university.scoreLineInfo.lowestScore || 0
      avgScore = university.scoreLineInfo.averageScore || 0
    } else {
      // 备用：从universityScoreLines映射中获取
      const scoreLines = universityScoreLines.get(university.SchoolUUID)
      if (!scoreLines || scoreLines.length === 0) {
        return {
          possibility: 'unknown',
          label: '暂无数据',
          color: 'text-gray-500'
        }
      }
      const latestScoreLine = scoreLines[0]
      minScore = parseInt(latestScoreLine.LowestScore) || latestScoreLine.lowestScore || 0
      avgScore = parseInt(latestScoreLine.AverageScore) || latestScoreLine.averageScore || 0
    }

    // 如果没有有效的分数数据，返回未知状态
    if (minScore === 0 && avgScore === 0) {
      return {
        possibility: 'unknown',
        label: '分数待定',
        color: 'text-gray-500',
        minScore,
        avgScore
      }
    }

    const userScore = userLatestScore.totalScore

    // 如果有平均分数据，使用平均分进行判断
    if (avgScore > 0) {
      if (userScore >= avgScore + 20) {
        return {
          possibility: 'high',
          label: '录取希望大',
          color: 'text-green-600',
          minScore,
          avgScore
        }
      } else if (userScore >= avgScore - 10) {
        return {
          possibility: 'medium',
          label: '有录取希望',
          color: 'text-yellow-600',
          minScore,
          avgScore
        }
      }
    }

    // 使用最低分进行判断
    if (minScore > 0) {
      if (userScore >= minScore + 30) {
        return {
          possibility: 'high',
          label: '录取希望大',
          color: 'text-green-600',
          minScore,
          avgScore
        }
      } else if (userScore >= minScore) {
        return {
          possibility: 'medium',
          label: '有录取希望',
          color: 'text-yellow-600',
          minScore,
          avgScore
        }
      } else if (userScore >= minScore - 20) {
        return {
          possibility: 'low',
          label: '录取希望较小',
          color: 'text-orange-600',
          minScore,
          avgScore
        }
      }
    }

    return {
      possibility: 'low',
      label: '录取困难',
      color: 'text-red-600',
      minScore,
      avgScore
    }
  }

  const handleSaveGoal = async () => {
    if (!user || !selectedUniversity) return

    try {
      setSaving(true)

      let goal: UserGoal

      if (goalType === 'university') {
        goal = {
          type: 'university',
          universityId: selectedUniversity.SchoolUUID,
          universityName: selectedUniversity.CollegeName,
          province: selectedUniversity.Province,
          is985: selectedUniversity.Is985,
          is211: selectedUniversity.Is211,
          isDualClass: selectedUniversity.IsDualClass,
          category: selectedUniversity.CollegeCategory,
          targetScore: targetScore ? parseInt(targetScore) : undefined
        } as UniversityGoal
      } else {
        if (!selectedMajor) {
          console.error('专业目标需要选择专业')
          return
        }

        goal = {
          type: 'major',
          universityId: selectedUniversity.SchoolUUID,
          universityName: selectedUniversity.CollegeName,
          majorId: selectedMajor.DataId || `${selectedMajor.MajorName}_${Date.now()}`,
          majorName: selectedMajor.MajorName,
          majorCode: selectedMajor.MajorCode,
          category: selectedMajor.MajorType,
          targetScore: targetScore ? parseInt(targetScore) : undefined
        } as MajorGoal
      }

      await goalService.setUserGoal({
        goal,
        userId: user.id
      })

      console.log('目标保存成功:', goal)
      // 返回到我的目标页面
      onNavigate?.('my-goal')
    } catch (error) {
      console.error('保存目标失败:', error)
      alert('保存目标失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const renderStepIndicator = () => {
    const steps = [
      { key: 'type', label: '选择类型' },
      { key: 'university', label: '选择大学' },
      ...(goalType === 'major' ? [{ key: 'major', label: '选择专业' }] : []),
      { key: 'confirm', label: '确认目标' }
    ]

    return (
      <div className="flex items-center justify-center space-x-4 mb-8">
        {steps.map((step, index) => (
          <div key={step.key} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep === step.key
                ? 'bg-orange-500 text-white'
                : steps.findIndex(s => s.key === currentStep) > index
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 text-gray-500'
            }`}>
              {steps.findIndex(s => s.key === currentStep) > index ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                index + 1
              )}
            </div>
            <span className={`ml-2 text-sm ${
              currentStep === step.key ? 'text-orange-600 font-medium' : 'text-gray-500'
            }`}>
              {step.label}
            </span>
            {index < steps.length - 1 && (
              <ChevronRight className="w-4 h-4 text-gray-300 mx-2" />
            )}
          </div>
        ))}
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">请先登录</h2>
          <p className="text-gray-500">登录后即可设置学习目标</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <Target className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">设置目标</h1>
                <p className="text-gray-500">选择您的理想大学或专业</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {renderStepIndicator()}

        {/* 步骤1: 选择目标类型 */}
        {currentStep === 'type' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 text-center">
                您想设置什么类型的目标？
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <button
                  onClick={() => {
                    setGoalType('university')
                    setCurrentStep('university')
                  }}
                  className="p-6 border-2 border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-all group"
                >
                  <School className="w-12 h-12 text-orange-500 mx-auto mb-4 group-hover:scale-110 transition-transform" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">大学目标</h3>
                  <p className="text-gray-500 text-sm">
                    选择一所理想的大学作为您的目标，追踪整体录取分数线
                  </p>
                </button>
                <button
                  onClick={() => {
                    setGoalType('major')
                    setCurrentStep('university')
                  }}
                  className="p-6 border-2 border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-all group"
                >
                  <BookOpen className="w-12 h-12 text-orange-500 mx-auto mb-4 group-hover:scale-110 transition-transform" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">专业目标</h3>
                  <p className="text-gray-500 text-sm">
                    选择特定大学的某个专业，追踪专业录取分数线
                  </p>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 步骤2: 选择大学 */}
        {currentStep === 'university' && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">选择大学</h2>

                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="搜索大学名称..."
                      value={universitySearchQuery}
                      onChange={(e) => handleUniversitySearch(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>

                  {/* 录取可能性筛选 */}
                  {userLatestScore && (
                    <div className="flex items-center space-x-4">
                      <span className="text-sm font-medium text-gray-700">录取可能性：</span>
                      <div className="flex space-x-2">
                        {[
                          { value: 'all', label: '全部', color: 'bg-gray-100 text-gray-700' },
                          { value: 'high', label: '希望大', color: 'bg-green-100 text-green-700' },
                          { value: 'medium', label: '有希望', color: 'bg-yellow-100 text-yellow-700' },
                          { value: 'low', label: '希望小', color: 'bg-red-100 text-red-700' }
                        ].map((filter) => (
                          <button
                            key={filter.value}
                            onClick={() => setAdmissionFilter(filter.value as any)}
                            className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                              admissionFilter === filter.value
                                ? filter.color + ' ring-2 ring-offset-1 ring-orange-300'
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                          >
                            {filter.label}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 用户分数信息提示 */}
              {userLatestScore && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2 text-sm text-blue-800">
                    <Award className="w-4 h-4" />
                    <span>基于您的最新成绩：{userLatestScore.totalScore}分 ({userLatestScore.examName})</span>
                  </div>
                </div>
              )}

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
                  <span className="ml-2 text-gray-500">搜索中...</span>
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {universities
                    .filter((university) => {
                      if (admissionFilter === 'all') return true
                      const admissionInfo = calculateAdmissionPossibility(university)
                      return admissionInfo.possibility === admissionFilter
                    })
                    .map((university) => {
                    const admissionInfo = calculateAdmissionPossibility(university)
                    const scoreLines = universityScoreLines.get(university.SchoolUUID)

                    return (
                      <button
                        key={university.SchoolUUID}
                        onClick={() => handleUniversitySelect(university)}
                        className="w-full p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-all text-left"
                      >
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3 flex-1">
                              {/* 学校logo */}
                              {university.CoverImage && (
                                <img
                                  src={university.CoverImage}
                                  alt={university.CollegeName}
                                  className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none'
                                  }}
                                />
                              )}

                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="font-medium text-gray-900 truncate">
                                    {university.CollegeName}
                                  </h3>
                                  {userLatestScore && (
                                    <span className={`text-sm font-medium ${admissionInfo.color} flex-shrink-0 ml-2`}>
                                      {admissionInfo.label}
                                    </span>
                                  )}
                                </div>

                                <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                                  <MapPin className="w-4 h-4 flex-shrink-0" />
                                  <span className="truncate">{university.Province} · {university.City}</span>
                                  {university.CollegeType && (
                                    <>
                                      <span>·</span>
                                      <span className={`text-xs px-1.5 py-0.5 rounded ${
                                        university.CollegeType === '公办'
                                          ? 'bg-green-100 text-green-700'
                                          : 'bg-orange-100 text-orange-700'
                                      }`}>
                                        {university.CollegeType}
                                      </span>
                                    </>
                                  )}
                                </div>

                                <div className="flex items-center flex-wrap gap-1">
                                  {university.Is985 && (
                                    <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded">985</span>
                                  )}
                                  {university.Is211 && (
                                    <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">211</span>
                                  )}
                                  {university.IsDualClass && (
                                    <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">双一流</span>
                                  )}
                                </div>
                              </div>
                            </div>
                            <ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0" />
                          </div>

                          {/* 录取分数线信息 */}
                          {(university.scoreLineInfo || (scoreLines && scoreLines.length > 0)) && (
                            <div className="p-3 bg-gray-50 rounded-lg text-xs space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-gray-600 font-medium">
                                  {university.scoreLineInfo?.year || 2024}年录取信息
                                </span>
                                {scoreLineLoading && (
                                  <Loader2 className="w-3 h-3 animate-spin text-gray-400" />
                                )}
                              </div>

                              {university.scoreLineInfo ? (
                                <>
                                  {/* 分数信息 */}
                                  <div className="grid grid-cols-2 gap-2">
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">最低分：</span>
                                      <span className="text-gray-900 font-medium">
                                        {university.scoreLineInfo.lowestScore || '-'}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">平均分：</span>
                                      <span className="text-gray-900 font-medium">
                                        {university.scoreLineInfo.averageScore || '-'}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">最高分：</span>
                                      <span className="text-gray-900 font-medium">
                                        {university.scoreLineInfo.highestScore || '-'}
                                      </span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">省控线：</span>
                                      <span className="text-gray-900 font-medium">
                                        {university.scoreLineInfo.provincialControlLine || '-'}
                                      </span>
                                    </div>
                                  </div>

                                  {/* 位次和批次信息 */}
                                  <div className="pt-1 border-t border-gray-200">
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="text-gray-600">录取位次：</span>
                                      <span className="text-gray-900 font-medium">
                                        {university.scoreLineInfo.lowestRank || '-'}
                                      </span>
                                    </div>
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="text-gray-600">录取批次：</span>
                                      <span className="text-blue-700 font-medium">
                                        {university.scoreLineInfo.admissionBatch}
                                      </span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <span className="text-gray-600">科目类型：</span>
                                      <span className="text-purple-700 font-medium">
                                        {university.scoreLineInfo.typeName}
                                      </span>
                                    </div>
                                  </div>

                                  {/* 选科要求 */}
                                  {university.scoreLineInfo.courseSelection && (
                                    <div className="pt-1 border-t border-gray-200">
                                      <div className="text-gray-600 mb-1">选科要求：</div>
                                      <div className="text-gray-900 text-xs bg-blue-50 px-2 py-1 rounded">
                                        {university.scoreLineInfo.courseSelection}
                                      </div>
                                      {university.scoreLineInfo.courseSelectionName && (
                                        <div className="text-gray-500 mt-1">
                                          专业组：{university.scoreLineInfo.courseSelectionName}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </>
                              ) : scoreLines && scoreLines.length > 0 && (
                                <div className="grid grid-cols-2 gap-2">
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">最低分：</span>
                                    <span className="text-gray-900 font-medium">
                                      {scoreLines[0].LowestScore || scoreLines[0].lowestScore || '-'}
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-600">平均分：</span>
                                    <span className="text-gray-900 font-medium">
                                      {scoreLines[0].AverageScore || scoreLines[0].averageScore || '-'}
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </button>
                    )
                  })}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 步骤3: 选择专业 */}
        {currentStep === 'major' && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  选择专业 - {selectedUniversity?.CollegeName}
                </h2>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="搜索专业名称..."
                    value={majorSearchQuery}
                    onChange={(e) => handleMajorSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                </div>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
                  <span className="ml-2 text-gray-500">搜索中...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                  {majors.length > 0 ? majors.map((major, index) => (
                    <button
                      key={major.DataId || index}
                      onClick={() => handleMajorSelect(major)}
                      className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-all text-left"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 mb-1">
                            {major.MajorName}
                          </h3>
                          {major.MajorCode && (
                            <p className="text-sm text-gray-500 mb-1">
                              专业代码: {major.MajorCode}
                            </p>
                          )}
                          {major.MajorType && (
                            <span className="inline-block px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">
                              {major.MajorType}
                            </span>
                          )}
                          {major.Degree && (
                            <span className="inline-block px-2 py-1 bg-green-100 text-green-600 text-xs rounded ml-2">
                              {major.Degree}
                            </span>
                          )}
                        </div>
                        <ChevronRight className="w-5 h-5 text-gray-400" />
                      </div>
                    </button>
                  )) : (
                    <div className="col-span-2 text-center py-12">
                      <BookOpen className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500">暂无专业数据</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 步骤4: 确认目标 */}
        {currentStep === 'confirm' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6 text-center">
                确认您的目标
              </h2>

              {/* 目标信息展示 */}
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
                    {goalType === 'university' ? (
                      <School className="w-6 h-6 text-white" />
                    ) : (
                      <BookOpen className="w-6 h-6 text-white" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {selectedUniversity?.CollegeName}
                    </h3>
                    {goalType === 'major' && selectedMajor && (
                      <p className="text-orange-600 font-medium mb-2">
                        {selectedMajor.MajorName}
                      </p>
                    )}
                    <div className="flex items-center space-x-2 text-sm text-gray-500 mb-3">
                      <MapPin className="w-4 h-4" />
                      <span>{selectedUniversity?.Province} · {selectedUniversity?.City}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {selectedUniversity?.Is985 && (
                        <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded">985</span>
                      )}
                      {selectedUniversity?.Is211 && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">211</span>
                      )}
                      {selectedUniversity?.IsDualClass && (
                        <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded">双一流</span>
                      )}
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                        {selectedUniversity?.CollegeCategory}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 目标分数设置 */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  目标分数 (可选)
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={targetScore}
                    onChange={(e) => setTargetScore(e.target.value)}
                    placeholder="如：600"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                    分
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  设置目标分数后，系统将帮您追踪进度并提供分析建议
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-between">
                <button
                  onClick={() => {
                    if (goalType === 'major') {
                      setCurrentStep('major')
                    } else {
                      setCurrentStep('university')
                    }
                  }}
                  className="flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                  <span>上一步</span>
                </button>
                <button
                  onClick={handleSaveGoal}
                  disabled={saving}
                  className="flex items-center space-x-2 px-8 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors disabled:opacity-50"
                >
                  {saving ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>保存中...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      <span>确认设置目标</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

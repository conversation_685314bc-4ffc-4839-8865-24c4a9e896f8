// 目标管理服务
import type { 
  UserGoal, 
  ExamScore, 
  GoalAnalysis, 
  SetGoalRequest, 
  AddScoreRequest,
  GoalListResponse,
  ScoreHistoryResponse
} from '../types/goal'

// 本地存储键名
const GOALS_STORAGE_KEY = 'user_goals'
const SCORES_STORAGE_KEY = 'exam_scores'

class GoalService {
  // 获取用户目标列表
  getUserGoals(userId: string): GoalListResponse {
    try {
      const storedGoals = localStorage.getItem(`${GOALS_STORAGE_KEY}_${userId}`)
      const goals: UserGoal[] = storedGoals ? JSON.parse(storedGoals) : []
      
      return {
        goals,
        total: goals.length
      }
    } catch (error) {
      console.error('获取用户目标失败:', error)
      return { goals: [], total: 0 }
    }
  }

  // 设置用户目标
  async setUserGoal(request: SetGoalRequest): Promise<UserGoal> {
    try {
      const { goal, userId } = request
      const currentGoals = this.getUserGoals(userId).goals
      
      // 检查是否已存在相同目标
      const existingIndex = currentGoals.findIndex(g => {
        if (g.type === 'university' && goal.type === 'university') {
          return g.universityId === goal.universityId
        } else if (g.type === 'major' && goal.type === 'major') {
          return g.universityId === goal.universityId && g.majorId === goal.majorId
        }
        return false
      })

      if (existingIndex >= 0) {
        // 更新现有目标
        currentGoals[existingIndex] = goal
      } else {
        // 添加新目标
        currentGoals.push(goal)
      }

      // 保存到本地存储
      localStorage.setItem(`${GOALS_STORAGE_KEY}_${userId}`, JSON.stringify(currentGoals))
      
      console.log('目标设置成功:', goal)
      return goal
    } catch (error) {
      console.error('设置目标失败:', error)
      throw error
    }
  }

  // 删除用户目标
  async deleteUserGoal(userId: string, goalId: string): Promise<void> {
    try {
      const currentGoals = this.getUserGoals(userId).goals
      const filteredGoals = currentGoals.filter(goal => {
        if (goal.type === 'university') {
          return goal.universityId !== goalId
        } else {
          return `${goal.universityId}_${goal.majorId}` !== goalId
        }
      })

      localStorage.setItem(`${GOALS_STORAGE_KEY}_${userId}`, JSON.stringify(filteredGoals))
      console.log('目标删除成功')
    } catch (error) {
      console.error('删除目标失败:', error)
      throw error
    }
  }

  // 添加考试分数
  async addExamScore(request: AddScoreRequest): Promise<ExamScore> {
    try {
      const { examScore, userId } = request
      const newScore: ExamScore = {
        ...examScore,
        id: `score_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const currentScores = this.getExamScores(userId).scores
      currentScores.push(newScore)
      
      // 按日期排序（最新的在前）
      currentScores.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      localStorage.setItem(`${SCORES_STORAGE_KEY}_${userId}`, JSON.stringify(currentScores))
      
      console.log('考试分数添加成功:', newScore)
      return newScore
    } catch (error) {
      console.error('添加考试分数失败:', error)
      throw error
    }
  }

  // 获取考试分数历史
  getExamScores(userId: string): ScoreHistoryResponse {
    try {
      const storedScores = localStorage.getItem(`${SCORES_STORAGE_KEY}_${userId}`)
      const scores: ExamScore[] = storedScores ? JSON.parse(storedScores) : []
      
      // 计算统计信息
      let analysis = undefined
      if (scores.length > 0) {
        const totalScores = scores.map(s => s.totalScore)
        const averageScore = totalScores.reduce((sum, score) => sum + score, 0) / totalScores.length
        const highestScore = Math.max(...totalScores)
        const lowestScore = Math.min(...totalScores)
        
        // 计算提升幅度（最近3次与之前的平均分比较）
        let improvementRate = 0
        if (scores.length >= 6) {
          const recentScores = scores.slice(0, 3).map(s => s.totalScore)
          const earlierScores = scores.slice(3, 6).map(s => s.totalScore)
          const recentAvg = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length
          const earlierAvg = earlierScores.reduce((sum, score) => sum + score, 0) / earlierScores.length
          improvementRate = ((recentAvg - earlierAvg) / earlierAvg) * 100
        }

        analysis = {
          averageScore: Math.round(averageScore * 10) / 10,
          highestScore,
          lowestScore,
          improvementRate: Math.round(improvementRate * 10) / 10
        }
      }

      return {
        scores,
        total: scores.length,
        analysis
      }
    } catch (error) {
      console.error('获取考试分数失败:', error)
      return { scores: [], total: 0 }
    }
  }

  // 删除考试分数
  async deleteExamScore(userId: string, scoreId: string): Promise<void> {
    try {
      const currentScores = this.getExamScores(userId).scores
      const filteredScores = currentScores.filter(score => score.id !== scoreId)

      localStorage.setItem(`${SCORES_STORAGE_KEY}_${userId}`, JSON.stringify(filteredScores))
      console.log('考试分数删除成功')
    } catch (error) {
      console.error('删除考试分数失败:', error)
      throw error
    }
  }

  // 分析目标达成情况
  analyzeGoalProgress(userId: string, goalId: string): GoalAnalysis | null {
    try {
      const goals = this.getUserGoals(userId).goals
      const scores = this.getExamScores(userId).scores

      // 找到目标
      const goal = goals.find(g => {
        if (g.type === 'university') {
          return g.universityId === goalId
        } else {
          return `${g.universityId}_${g.majorId}` === goalId
        }
      })

      if (!goal || !goal.targetScore || scores.length === 0) {
        return null
      }

      // 获取最新分数
      const latestScore = scores[0]
      const currentScore = latestScore.totalScore
      const targetScore = goal.targetScore
      const scoreDifference = targetScore - currentScore
      const isAchieved = currentScore >= targetScore
      
      // 计算进度百分比
      const progress = Math.min(Math.max((currentScore / targetScore) * 100, 0), 100)

      // 分析趋势
      let trend: 'improving' | 'stable' | 'declining' = 'stable'
      if (scores.length >= 3) {
        const recentScores = scores.slice(0, 3).map(s => s.totalScore)
        const avgRecent = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length
        const olderScores = scores.slice(3, 6).map(s => s.totalScore)
        if (olderScores.length > 0) {
          const avgOlder = olderScores.reduce((sum, score) => sum + score, 0) / olderScores.length
          if (avgRecent > avgOlder + 5) {
            trend = 'improving'
          } else if (avgRecent < avgOlder - 5) {
            trend = 'declining'
          }
        }
      }

      // 生成建议
      let recommendation = ''
      if (isAchieved) {
        recommendation = `恭喜！您已达到${goal.universityName}${goal.type === 'major' ? goal.majorName : ''}的目标分数。建议设置更高的目标继续努力！`
      } else {
        const monthsNeeded = Math.ceil(scoreDifference / 10) // 假设每月能提高10分
        recommendation = `还需提高${scoreDifference}分。按当前进度，预计需要${monthsNeeded}个月达到目标。建议重点关注薄弱科目。`
      }

      // 下一个里程碑
      let nextMilestone = undefined
      if (!isAchieved) {
        const milestoneScore = Math.min(currentScore + 50, targetScore)
        nextMilestone = {
          score: milestoneScore,
          description: `下一个目标：${milestoneScore}分`
        }
      }

      return {
        goalId,
        currentScore,
        targetScore,
        scoreDifference,
        isAchieved,
        progress: Math.round(progress * 10) / 10,
        trend,
        recommendation,
        nextMilestone
      }
    } catch (error) {
      console.error('分析目标进度失败:', error)
      return null
    }
  }
}

// 导出单例实例
export const goalService = new GoalService()

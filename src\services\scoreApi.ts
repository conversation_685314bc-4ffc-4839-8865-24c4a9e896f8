// 分数线相关API服务
import { ApiType, getApiKey, getApiPath } from './apiKeys'

// API配置
const isDevelopment = import.meta.env?.MODE === 'development' || typeof window !== 'undefined'
const API_BASE_URL = isDevelopment ? 'http://localhost:3001/api/gugudata' : 'https://api.gugudata.com'

// 一分一段数据接口
export interface ScoreSegmentFilters {
  keywords?: string      // 搜索关键字
  pagesize?: number      // 每页数据量
  pageindex?: number     // 页码
  province?: string      // 省份
  year?: string          // 年份
  category?: string      // 科类
}

// 省录取分数线接口
export interface ProvinceScoreFilters {
  keywords?: string      // 搜索关键字
  pagesize?: number      // 每页数据量
  pageindex?: number     // 页码
  province?: string      // 省份
  year?: string          // 年份
  batch?: string         // 批次
}

// 高校录取分数线接口
export interface SchoolScoreFilters {
  keywords?: string      // 搜索关键字
  pagesize?: number      // 每页数据量
  pageindex?: number     // 页码
  province?: string      // 省份
  year?: string          // 年份
  schoolname?: string    // 学校名称
}

/**
 * 构建查询参数 - 一分一段
 */
function buildScoreSegmentParams(filters: ScoreSegmentFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', getApiKey(ApiType.SCORE))
  
  if (filters.keywords !== undefined) params.append('keywords', filters.keywords)
  if (filters.pagesize !== undefined) params.append('pagesize', filters.pagesize.toString())
  if (filters.pageindex !== undefined) params.append('pageindex', filters.pageindex.toString())
  if (filters.province) params.append('province', filters.province)
  if (filters.year) params.append('year', filters.year)
  if (filters.category) params.append('category', filters.category)
  
  return params
}

/**
 * 构建查询参数 - 省录取分数线
 */
function buildProvinceScoreParams(filters: ProvinceScoreFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', getApiKey(ApiType.PROVINCE))
  
  if (filters.keywords !== undefined) params.append('keywords', filters.keywords)
  if (filters.pagesize !== undefined) params.append('pagesize', filters.pagesize.toString())
  if (filters.pageindex !== undefined) params.append('pageindex', filters.pageindex.toString())
  if (filters.province) params.append('province', filters.province)
  if (filters.year) params.append('year', filters.year)
  if (filters.batch) params.append('batch', filters.batch)
  
  return params
}

/**
 * 构建查询参数 - 高校录取分数线
 */
function buildSchoolScoreParams(filters: SchoolScoreFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', getApiKey(ApiType.SCHOOL_SCORE))
  
  if (filters.keywords !== undefined) params.append('keywords', filters.keywords)
  if (filters.pagesize !== undefined) params.append('pagesize', filters.pagesize.toString())
  if (filters.pageindex !== undefined) params.append('pageindex', filters.pageindex.toString())
  if (filters.province) params.append('province', filters.province)
  if (filters.year) params.append('year', filters.year)
  if (filters.schoolname) params.append('schoolname', filters.schoolname)
  
  return params
}

/**
 * 获取一分一段数据
 */
export async function getScoreSegment(filters: ScoreSegmentFilters = {}): Promise<any> {
  try {
    const params = buildScoreSegmentParams(filters)
    const apiPath = getApiPath(ApiType.SCORE)
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`
    
    console.log('🔍 请求一分一段数据:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 一分一段数据获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 一分一段数据获取失败:', error)
    throw error
  }
}

/**
 * 获取省录取分数线
 */
export async function getProvinceScore(filters: ProvinceScoreFilters = {}): Promise<any> {
  try {
    const params = buildProvinceScoreParams(filters)
    const apiPath = getApiPath(ApiType.PROVINCE)
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`
    
    console.log('🔍 请求省录取分数线:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 省录取分数线获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 省录取分数线获取失败:', error)
    throw error
  }
}

/**
 * 获取高校录取分数线
 */
export async function getSchoolScore(filters: SchoolScoreFilters = {}): Promise<any> {
  try {
    const params = buildSchoolScoreParams(filters)
    const apiPath = getApiPath(ApiType.SCHOOL_SCORE)
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`
    
    console.log('🔍 请求高校录取分数线:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 高校录取分数线获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 高校录取分数线获取失败:', error)
    throw error
  }
}

console.log('🔧 分数线API服务已加载')
console.log('- 一分一段API密钥:', getApiKey(ApiType.SCORE).substring(0, 8) + '...')
console.log('- 省分数线API密钥:', getApiKey(ApiType.PROVINCE).substring(0, 8) + '...')
console.log('- 高校分数线API密钥:', getApiKey(ApiType.SCHOOL_SCORE).substring(0, 8) + '...')

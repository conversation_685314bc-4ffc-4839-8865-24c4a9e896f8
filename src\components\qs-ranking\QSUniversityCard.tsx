import { useState } from 'react'
import { Card } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import type { ProcessedQSUniversity } from '../../types/qs-ranking'
import { QS_RANKING_INDICATORS } from '../../types/qs-ranking'
import { cn } from '../../lib/utils'
import {
  Globe,
  MapPin,
  Trophy,
  TrendingUp,
  Users,
  Award,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Star,
  BarChart3,
  Languages
} from 'lucide-react'

interface QSUniversityCardProps {
  university: ProcessedQSUniversity
  viewMode?: 'grid' | 'list'
}

export function QSUniversityCard({
  university,
  viewMode = 'grid'
}: QSUniversityCardProps) {
  const [showDetails, setShowDetails] = useState(false)
  const [showChinese, setShowChinese] = useState(true)

  const getRankBadgeColor = (rank: number) => {
    if (rank <= 10) return 'bg-yellow-500 text-white'
    if (rank <= 50) return 'bg-orange-500 text-white'
    if (rank <= 100) return 'bg-blue-500 text-white'
    return 'bg-gray-500 text-white'
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-orange-600'
    return 'text-gray-600'
  }

  const getScoreBarWidth = (score: number) => {
    return Math.min(100, Math.max(0, score))
  }

  const topScores = [
    {
      key: 'academicReputation',
      name: '学术声誉',
      score: university.scores.academicReputation.score,
      rank: university.scores.academicReputation.rank
    },
    {
      key: 'employerReputation',
      name: '雇主声誉',
      score: university.scores.employerReputation.score,
      rank: university.scores.employerReputation.rank
    },
    {
      key: 'facultyStudentRatio',
      name: '师生比例',
      score: university.scores.facultyStudentRatio.score,
      rank: university.scores.facultyStudentRatio.rank
    }
  ].sort((a, b) => b.score - a.score).slice(0, 3)

  if (viewMode === 'list') {
    return (
      <Card className="p-4 hover:shadow-lg transition-all duration-200">
        <div className="flex items-center gap-4">
          {/* Rank Badge */}
          <div className={cn(
            "flex-shrink-0 w-16 h-16 rounded-lg flex items-center justify-center font-bold text-lg",
            getRankBadgeColor(university.rank)
          )}>
            #{university.rank}
          </div>

          {/* University Logo */}
          <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
            {university.logoUrl ? (
              <img 
                src={university.logoUrl} 
                alt={`${university.name} logo`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none'
                }}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Globe className="w-6 h-6 text-white" />
              </div>
            )}
          </div>

          {/* University Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-bold text-lg text-gray-900 truncate">
                {showChinese && university.nameZh !== university.name
                  ? university.nameZh
                  : university.name}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowChinese(!showChinese)}
                className="p-1 h-6 w-6 text-gray-500 hover:text-blue-600"
              >
                <Languages className="w-4 h-4" />
              </Button>
            </div>
            {showChinese && university.nameZh !== university.name && (
              <p className="text-sm text-gray-500 mb-1">{university.name}</p>
            )}
            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                <span>
                  {showChinese && university.countryZh !== university.country
                    ? university.countryZh
                    : university.country}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Globe className="w-4 h-4" />
                <span>
                  {showChinese && university.regionZh !== university.region
                    ? university.regionZh
                    : university.region}
                </span>
              </div>
            </div>
          </div>

          {/* Top Scores */}
          <div className="flex-shrink-0 flex gap-4">
            {topScores.map((score) => (
              <div key={score.key} className="text-center">
                <div className={cn("text-lg font-bold", getScoreColor(score.score))}>
                  {score.score.toFixed(1)}
                </div>
                <div className="text-xs text-gray-500">{score.name}</div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        {/* Detailed Scores */}
        {showDetails && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {QS_RANKING_INDICATORS.map((indicator) => {
                const score = university.scores[indicator.key as keyof typeof university.scores]
                return (
                  <div key={indicator.key} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">
                        {indicator.name}
                      </span>
                      <span className={cn("text-sm font-bold", getScoreColor(score.score))}>
                        {score.score.toFixed(1)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getScoreBarWidth(score.score)}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500">
                      排名: #{score.rank} | 权重: {indicator.weight}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </Card>
    )
  }

  // Grid view
  return (
    <Card className="p-6 hover:shadow-xl transition-all duration-300 group relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-full -translate-y-16 translate-x-16 group-hover:scale-110 transition-transform duration-300" />
      
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {/* University Logo */}
            <div className="w-16 h-16 bg-gray-100 rounded-xl overflow-hidden flex-shrink-0">
              {university.logoUrl ? (
                <img 
                  src={university.logoUrl} 
                  alt={`${university.name} logo`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <Globe className="w-8 h-8 text-white" />
                </div>
              )}
            </div>

            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-bold text-lg text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors flex-1">
                  {showChinese && university.nameZh !== university.name
                    ? university.nameZh
                    : university.name}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowChinese(!showChinese)}
                  className="p-1 h-6 w-6 text-gray-500 hover:text-blue-600 flex-shrink-0"
                >
                  <Languages className="w-4 h-4" />
                </Button>
              </div>
              {showChinese && university.nameZh !== university.name && (
                <p className="text-xs text-gray-500 mb-1 line-clamp-1">{university.name}</p>
              )}
              <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                <MapPin className="w-4 h-4" />
                <span>
                  {showChinese && university.cityZh !== university.city
                    ? university.cityZh
                    : university.city}, {' '}
                  {showChinese && university.countryZh !== university.country
                    ? university.countryZh
                    : university.country}
                </span>
              </div>
            </div>
          </div>

          {/* Rank Badge */}
          <Badge className={cn(
            "text-lg font-bold px-3 py-1",
            getRankBadgeColor(university.rank)
          )}>
            #{university.rank}
          </Badge>
        </div>

        {/* Region Badge */}
        <div className="mb-4">
          <Badge variant="outline" className="text-xs">
            <Globe className="w-3 h-3 mr-1" />
            {showChinese && university.regionZh !== university.region
              ? university.regionZh
              : university.region}
          </Badge>
        </div>

        {/* Top Scores */}
        <div className="space-y-3 mb-4">
          <h4 className="text-sm font-semibold text-gray-700 flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            核心指标
          </h4>
          {topScores.map((score) => (
            <div key={score.key} className="flex items-center justify-between">
              <span className="text-sm text-gray-600">{score.name}</span>
              <div className="flex items-center gap-2">
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                    style={{ width: `${getScoreBarWidth(score.score)}%` }}
                  />
                </div>
                <span className={cn("text-sm font-bold w-12 text-right", getScoreColor(score.score))}>
                  {score.score.toFixed(1)}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            className="flex-1"
          >
            {showDetails ? (
              <>
                <ChevronUp className="w-4 h-4 mr-1" />
                收起详情
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4 mr-1" />
                查看详情
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="px-3"
          >
            <Star className="w-4 h-4" />
          </Button>
        </div>

        {/* Detailed Information */}
        {showDetails && (
          <div className="mt-4 pt-4 border-t border-gray-200 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {QS_RANKING_INDICATORS.slice(0, 6).map((indicator) => {
                const score = university.scores[indicator.key as keyof typeof university.scores]
                return (
                  <div key={indicator.key} className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs font-medium text-gray-700">
                        {indicator.name}
                      </span>
                      <span className={cn("text-xs font-bold", getScoreColor(score.score))}>
                        {score.score.toFixed(1)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-1.5 rounded-full"
                        style={{ width: `${getScoreBarWidth(score.score)}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500">
                      排名 #{score.rank}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}
